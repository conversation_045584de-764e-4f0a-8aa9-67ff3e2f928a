<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tydic.nicc</groupId>
        <artifactId>nicc-common</artifactId>
        <version>1.6.6</version>
    </parent>

    <groupId>com.tydic.nbchat</groupId>
    <artifactId>nbchat-train</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>nbchat-train</name>
    <description>培训学习</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.7.8</revision>
        <pdfbox.version>2.0.27</pdfbox.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tydic.nicc.dc</groupId>
                <artifactId>dc-boot-starter</artifactId>
                <version>${nicc-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kryo</artifactId>
                        <groupId>com.esotericsoftware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>aws-java-sdk-s3</artifactId>
                        <groupId>com.amazonaws</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cos_api</artifactId>
                        <groupId>com.qcloud</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.huaweicloud</groupId>
                        <artifactId>esdk-obs-java-bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-robot-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-admin-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-user-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-train-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-train-mapper</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-train-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-train-report</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>3.0.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire.version}</version>
                <configuration>
                    <!--默认关掉单元测试 -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.0.8</version>
                <configuration>
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <projectName>${project.description}</projectName>
                    <includes>
                        <include>com.baomidou:mybatis-plus-extension</include>
                        <include>com.baomidou:mybatis-plus-core</include>
                        <include>org.springframework.data:spring-data-commons</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--If you do not need to start smart-doc when performing compilation, comment out phase-->
                        <phase>compile</phase>
                        <goals>
                            <!--smart-doc provides HTML, openapi, markdown and other goals, which can be configured as needed.-->
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://118.190.78.212:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://118.190.78.212:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <modules>
        <module>nbchat-train-api</module>
        <module>nbchat-train-service</module>
        <module>nbchat-train-core</module>
        <module>nbchat-train-report</module>
        <module>nbchat-train-mapper</module>
    </modules>

</project>
