package com.tydic.nbchat.admin.api.bo;

import com.tydic.nbchat.admin.api.bo.post.SysPostBO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysTplUserRelRspBO implements Serializable {
    private Integer id;
    /**
     * 真实姓名
     */
    private String userRealityName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 模板编码
     */
    private String tplCode;
    /**
     * 模板名称
     */
    private String tplName;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 排序用
     */
    private String sort;

}
