package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import org.springframework.web.multipart.MultipartFile;

public interface StudentManagementApi {
    /**
     * 新增学员
     *
     * @param @param studentManagementReqBO 学生管理要求博
     * @return @return {@link Rsp }
     */
    Rsp addNewStudent(SysDeptUserSaveReqBO studentManagementReqBO);

    /**
     * 查询学员信息
     *
     * @param @param studentManagementReqBO 学生管理要求博
     * @return @return {@link Rsp }
     */
    Rsp getUserInfoQR(SysDeptUserQueryReqBO deptUserQueryReqBO);

    Rsp getUserInfoQR(String tenantCode, String userId);

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    Rsp importUsers(MultipartFile file, String tenantCode, String clientUa, String clientIp);

    /**
     * 同步用户详情
     *
     * @return
     */
    Rsp syncUserDetail();
}
