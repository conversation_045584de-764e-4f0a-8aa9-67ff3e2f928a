package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.SysTplUserRelReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SysTplUserRelApi {
    /**
     * 创建用户关联模板
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp createUserTplRel(SysTplUserRelReqBO reqBO);

    /**
     * 查询用户关联模板集合
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    RspList getUserRelList(SysTplUserRelReqBO reqBO);

    /**
     * 更新用户关联模板信息
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp updateUserTplRel(SysTplUserRelReqBO reqBO);


    /**
     * 删除用户关联模板信息
     *
     */
    Rsp deleteUserTplRel(SysTplUserRelReqBO reqBO);

    /**
     * 获取单个用户关联模板信息
     */
    Rsp getUserTplRelInfo(SysTplUserRelReqBO reqBO);

    /**
     * 获取用户的菜单
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp getUserMenu(SysTplUserRelReqBO reqBO);
}
