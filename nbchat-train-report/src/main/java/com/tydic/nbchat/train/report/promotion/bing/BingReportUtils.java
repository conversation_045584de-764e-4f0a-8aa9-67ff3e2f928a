package com.tydic.nbchat.train.report.promotion.bing;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类
 */
@Slf4j
public class BingReportUtils {

    /**
     * 从SOAP响应中提取报告请求ID
     */
    public static String extractReportRequestId(String soapResponse) {
        try {
            log.debug("【Bing工具】开始解析报告请求ID，响应长度: {}", soapResponse != null ? soapResponse.length() : 0);

            if (soapResponse == null || soapResponse.trim().isEmpty()) {
                log.error("【Bing工具】SOAP响应为空");
                return null;
            }

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(soapResponse.getBytes("UTF-8")));

            // 尝试多种可能的标签名
            String[] possibleTags = {"ReportRequestId", "reportRequestId", "RequestId", "requestId"};

            for (String tagName : possibleTags) {
                NodeList nodes = document.getElementsByTagName(tagName);
                if (nodes.getLength() > 0) {
                    String requestId = nodes.item(0).getTextContent();
                    log.info("【Bing工具】成功提取报告请求ID: {}", requestId);
                    return requestId;
                }
            }

            log.warn("【Bing工具】未在SOAP响应中找到ReportRequestId，响应内容: {}", soapResponse);
            return null;

        } catch (Exception e) {
            log.error("【Bing工具】解析报告请求ID失败，响应内容: " + soapResponse, e);
            return null;
        }
    }

    /**
     * 构建查询报告状态的SOAP请求
     */
    public static String buildPollGenerateReportRequest(String customerId, String developerToken,
                                                       String customerAccountId, String accessToken,
                                                       String reportRequestId) {
        log.debug("【Bing工具】构建轮询请求，customerId: {}, customerAccountId: {}, reportRequestId: {}",
                customerId, customerAccountId, reportRequestId);

        StringBuilder soapBody = new StringBuilder();
        soapBody.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        soapBody.append("<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">");

        // 使用正确的SOAP Header格式
        soapBody.append("<s:Header xmlns:h=\"https://bingads.microsoft.com/Reporting/v13\">");

        // 可选：操作名称
        soapBody.append("<h:Action s:mustUnderstand=\"1\">PollGenerateReport</h:Action>");

        // 必填：开发者令牌
        soapBody.append("<h:DeveloperToken>").append(developerToken).append("</h:DeveloperToken>");

        // 必填：OAuth access_token
        soapBody.append("<h:AuthenticationToken>").append(accessToken).append("</h:AuthenticationToken>");

        // 必填：CustomerId（经理账号ID）
        soapBody.append("<h:CustomerId>").append(customerId).append("</h:CustomerId>");

        // 必填：CustomerAccountId（广告账户ID）
        if (StringUtils.isNotBlank(customerAccountId)) {
            soapBody.append("<h:CustomerAccountId>").append(customerAccountId).append("</h:CustomerAccountId>");
        }

        soapBody.append("</s:Header>");
        soapBody.append("<s:Body>");
        soapBody.append("<PollGenerateReportRequest xmlns=\"https://bingads.microsoft.com/Reporting/v13\">");
        soapBody.append("<ReportRequestId>").append(reportRequestId).append("</ReportRequestId>");
        soapBody.append("</PollGenerateReportRequest>");
        soapBody.append("</s:Body>");
        soapBody.append("</s:Envelope>");

        return soapBody.toString();
    }

    /**
     * 从轮询响应中提取报告状态和下载URL
     */
    public static ReportStatus extractReportStatus(String soapResponse) {
        try {
            log.debug("【Bing工具】开始解析报告状态，响应长度: {}", soapResponse != null ? soapResponse.length() : 0);

            if (soapResponse == null || soapResponse.trim().isEmpty()) {
                log.error("【Bing工具】SOAP响应为空");
                return null;
            }

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(soapResponse.getBytes("UTF-8")));

            ReportStatus status = new ReportStatus();

            // 获取报告状态
            String[] statusTags = {"ReportRequestStatus", "reportRequestStatus", "Status", "status"};
            for (String tagName : statusTags) {
                NodeList statusNodes = document.getElementsByTagName(tagName);
                if (statusNodes.getLength() > 0) {
                    String statusValue = statusNodes.item(0).getTextContent();
                    status.setStatus(statusValue);
                    log.info("【Bing工具】提取到报告状态: {}", statusValue);
                    break;
                }
            }

            // 获取下载URL
            String[] urlTags = {"ReportDownloadUrl", "reportDownloadUrl", "DownloadUrl", "downloadUrl"};
            for (String tagName : urlTags) {
                NodeList urlNodes = document.getElementsByTagName(tagName);
                if (urlNodes.getLength() > 0) {
                    String downloadUrl = urlNodes.item(0).getTextContent();
                    status.setDownloadUrl(downloadUrl);
                    log.info("【Bing工具】提取到下载URL: {}", downloadUrl);
                    break;
                }
            }

            if (status.getStatus() == null) {
                log.warn("【Bing工具】未能提取到报告状态，响应内容: {}", soapResponse);
            }

            return status;

        } catch (Exception e) {
            log.error("【Bing工具】解析报告状态失败，响应内容: " + soapResponse, e);
            return null;
        }
    }

    /**
     * 下载CSV报告数据
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> csvData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始下载CSV报告，URL: {}", downloadUrl);

            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000); // 60秒读取超时
            connection.setRequestProperty("User-Agent", "NBChat-BingAds-Client/1.0");

            int responseCode = connection.getResponseCode();
            log.info("【Bing工具】CSV下载响应码: {}", responseCode);

            if (responseCode != 200) {
                log.error("【Bing工具】CSV下载失败，响应码: {}", responseCode);
                return csvData;
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            boolean isFirstLine = true;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                lineCount++;

                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("【Bing工具】CSV标题行: {}", line);
                    continue;
                }

                // 解析CSV行
                String[] fields = parseCsvLine(line);
                if (fields != null && fields.length > 0) {
                    csvData.add(fields);
                }
            }

            reader.close();
            connection.disconnect();

            log.info("【Bing工具】CSV下载完成，总行数: {}，数据行数: {}", lineCount, csvData.size());

        } catch (Exception e) {
            log.error("【Bing工具】下载CSV报告失败，URL: " + downloadUrl, e);
        }

        return csvData;
    }

    /**
     * 解析CSV行，处理引号和逗号
     */
    private static String[] parseCsvLine(String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }
        
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString().trim());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equals(status);
        }

        public boolean isPending() {
            return "Pending".equals(status);
        }

        public boolean isError() {
            return "Error".equals(status);
        }
    }
}
