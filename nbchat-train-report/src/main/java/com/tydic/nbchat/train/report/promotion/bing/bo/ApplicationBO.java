package com.tydic.nbchat.train.report.promotion.bing.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告应用配置
 */
@Data
public class ApplicationBO {
    
    /**
     * 客户ID
     */
    private String customerId;
    
    /**
     * 开发者Token
     */
    private String developerToken;
    
    /**
     * 应用客户端ID
     */
    private String clientId;
    
    /**
     * 应用客户端密钥
     */
    private String clientSecret;
    
    /**
     * 刷新Token
     */
    private String refreshToken;
    
    /**
     * 应用名称
     */
    private String appName;

    /**
     * 客户账户ID (广告账户ID)
     * 注意：这与customerId不同，customerId是经理账号ID，customerAccountId是具体的广告账户ID
     */
    private String customerAccountId;
}
