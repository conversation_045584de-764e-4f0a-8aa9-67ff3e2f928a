package com.tydic.nbchat.train.report.promotion.bing;

import com.microsoft.bingads.*;
import com.microsoft.bingads.v13.reporting.*;
import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/30
 * @description Bing Ads SDK 服务类
 */
@Slf4j
@Service
public class BingSdkService {

    private static final int MAX_POLL_ATTEMPTS = 30; // 最多轮询30次
    private static final int POLL_INTERVAL_SECONDS = 10; // 每10秒轮询一次

    /**
     * 使用 Bing Ads SDK 查询搜索日志
     */
    public List<RpSearchLog> findSearchLogWithSdk(ApplicationBO application, String accessToken,
                                                  Date startDate, Date endDate,
                                                  PromotionTypeEnum promotionType, int limit) {
        try {
            log.info("【Bing SDK】开始查询搜索日志，customerId: {}, customerAccountId: {}", 
                    application.getCustomerId(), application.getCustomerAccountId());

            // 创建认证对象
            AuthorizationData authorizationData = createAuthorizationData(application, accessToken);
            
            // 创建报告服务代理
            ServiceClient<IReportingService> reportingService = ReportingServiceManager.getInstance()
                    .getReportingServiceClient(authorizationData, ApiEnvironment.PRODUCTION);

            // 构建报告请求
            SearchQueryPerformanceReportRequest reportRequest = buildSearchQueryReportRequest(
                    application, startDate, endDate, promotionType);

            // 提交报告生成请求
            SubmitGenerateReportResponse submitResponse = reportingService.getService()
                    .submitGenerateReport(reportRequest);
            
            String reportRequestId = submitResponse.getReportRequestId();
            log.info("【Bing SDK】报告请求提交成功，reportRequestId: {}", reportRequestId);

            // 轮询报告状态
            String downloadUrl = pollReportStatus(reportingService, reportRequestId);
            if (StringUtils.isBlank(downloadUrl)) {
                log.error("【Bing SDK】获取报告下载URL失败");
                return new ArrayList<>();
            }

            // 下载并解析报告
            return downloadAndParseReport(downloadUrl, promotionType);

        } catch (Exception e) {
            log.error("【Bing SDK】查询搜索日志失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建认证数据
     */
    private AuthorizationData createAuthorizationData(ApplicationBO application, String accessToken) {
        AuthorizationData authorizationData = new AuthorizationData();
        
        // 设置认证信息
        authorizationData.setAuthentication(new OAuthWebAuthCodeGrant(
                application.getClientId(),
                application.getClientSecret(),
                "http://localhost:8705/train/callback/bing", // 重定向URI
                ApiEnvironment.PRODUCTION
        ));
        
        // 设置访问令牌
        ((OAuthWebAuthCodeGrant) authorizationData.getAuthentication()).setOAuthTokens(
                new OAuthTokens(accessToken, null, null, null)
        );
        
        // 设置客户ID和账户ID
        authorizationData.setCustomerId(Long.parseLong(application.getCustomerId()));
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            authorizationData.setAccountId(Long.parseLong(application.getCustomerAccountId()));
        }
        
        // 设置开发者令牌
        authorizationData.setDeveloperToken(application.getDeveloperToken());
        
        log.info("【Bing SDK】认证数据创建成功，customerId: {}, accountId: {}", 
                authorizationData.getCustomerId(), authorizationData.getAccountId());
        
        return authorizationData;
    }

    /**
     * 构建搜索查询性能报告请求
     */
    private SearchQueryPerformanceReportRequest buildSearchQueryReportRequest(
            ApplicationBO application, Date startDate, Date endDate, PromotionTypeEnum promotionType) {
        
        SearchQueryPerformanceReportRequest reportRequest = new SearchQueryPerformanceReportRequest();
        
        // 基本设置
        reportRequest.setReportName("SearchQueryPerformanceReport");
        reportRequest.setFormat(ReportFormat.CSV);
        reportRequest.setLanguage(ReportLanguage.ENGLISH);
        reportRequest.setReturnOnlyCompleteData(false);
        reportRequest.setExcludeReportHeader(true);
        reportRequest.setExcludeReportFooter(true);
        reportRequest.setExcludeColumnHeaders(false);

        // 时间范围
        ReportTime reportTime = new ReportTime();
        reportTime.setCustomDateRangeStart(createDate(startDate));
        reportTime.setCustomDateRangeEnd(createDate(endDate));
        reportRequest.setTime(reportTime);

        // 账户范围
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            AccountThroughAccountReportScope scope = new AccountThroughAccountReportScope();
            ArrayOflong accountIds = new ArrayOflong();
            accountIds.getLongs().add(Long.parseLong(application.getCustomerAccountId()));
            scope.setAccountIds(accountIds);
            reportRequest.setScope(scope);
        }

        // 列定义
        ArrayOfSearchQueryPerformanceReportColumn columns = new ArrayOfSearchQueryPerformanceReportColumn();
        columns.getSearchQueryPerformanceReportColumns().addAll(Arrays.asList(
                SearchQueryPerformanceReportColumn.TIME_PERIOD,
                SearchQueryPerformanceReportColumn.SEARCH_QUERY,
                SearchQueryPerformanceReportColumn.CAMPAIGN_NAME,
                SearchQueryPerformanceReportColumn.AD_GROUP_NAME,
                SearchQueryPerformanceReportColumn.KEYWORD,
                SearchQueryPerformanceReportColumn.IMPRESSIONS,
                SearchQueryPerformanceReportColumn.CLICKS,
                SearchQueryPerformanceReportColumn.SPEND,
                SearchQueryPerformanceReportColumn.CTR,
                SearchQueryPerformanceReportColumn.AVERAGE_CPC,
                SearchQueryPerformanceReportColumn.DEVICE_TYPE,
                SearchQueryPerformanceReportColumn.DELIVERED_MATCH_TYPE
        ));
        reportRequest.setColumns(columns);

        // 过滤器
        if (promotionType != null && !PromotionTypeEnum.ALL_PROMOTION_TYPE.equals(promotionType)) {
            SearchQueryPerformanceReportFilter filter = new SearchQueryPerformanceReportFilter();
            ArrayOfDeviceTypeReportFilter deviceTypes = new ArrayOfDeviceTypeReportFilter();
            
            if (PromotionTypeEnum.COMPUTER_PROMOTION_TYPE.equals(promotionType)) {
                deviceTypes.getDeviceTypeReportFilters().add(DeviceTypeReportFilter.COMPUTER);
            } else if (PromotionTypeEnum.MOBILE_PROMOTION_TYPE.equals(promotionType)) {
                deviceTypes.getDeviceTypeReportFilters().add(DeviceTypeReportFilter.SMARTPHONE);
                deviceTypes.getDeviceTypeReportFilters().add(DeviceTypeReportFilter.TABLET);
            }
            
            filter.setDeviceType(deviceTypes);
            reportRequest.setFilter(filter);
        }

        log.info("【Bing SDK】报告请求构建完成，时间范围: {} 到 {}", startDate, endDate);
        return reportRequest;
    }

    /**
     * 创建日期对象
     */
    private Date createDate(java.util.Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        
        Date reportDate = new Date();
        reportDate.setYear(calendar.get(Calendar.YEAR));
        reportDate.setMonth(calendar.get(Calendar.MONTH) + 1); // Calendar.MONTH 是从0开始的
        reportDate.setDay(calendar.get(Calendar.DAY_OF_MONTH));
        
        return reportDate;
    }

    /**
     * 轮询报告状态
     */
    private String pollReportStatus(ServiceClient<IReportingService> reportingService, String reportRequestId) {
        try {
            for (int attempt = 1; attempt <= MAX_POLL_ATTEMPTS; attempt++) {
                log.info("【Bing SDK】轮询报告状态，第{}次尝试", attempt);
                
                // 等待
                if (attempt > 1) {
                    TimeUnit.SECONDS.sleep(POLL_INTERVAL_SECONDS);
                }

                // 查询报告状态
                PollGenerateReportResponse pollResponse = reportingService.getService()
                        .pollGenerateReport(reportRequestId);

                ReportRequestStatus status = pollResponse.getReportRequestStatus();
                log.info("【Bing SDK】报告状态: {}", status.getStatus());

                if (status.getStatus() == ReportRequestStatusType.SUCCESS) {
                    String downloadUrl = status.getReportDownloadUrl();
                    log.info("【Bing SDK】报告生成成功，下载URL: {}", downloadUrl);
                    return downloadUrl;
                } else if (status.getStatus() == ReportRequestStatusType.ERROR) {
                    log.error("【Bing SDK】报告生成失败");
                    return null;
                } else if (status.getStatus() == ReportRequestStatusType.PENDING) {
                    log.info("【Bing SDK】报告正在生成中，继续等待...");
                    continue;
                }
            }

            log.error("【Bing SDK】报告生成超时，已尝试{}次", MAX_POLL_ATTEMPTS);
            return null;

        } catch (Exception e) {
            log.error("【Bing SDK】轮询报告状态失败", e);
            return null;
        }
    }

    /**
     * 下载并解析报告
     */
    private List<RpSearchLog> downloadAndParseReport(String downloadUrl, PromotionTypeEnum promotionType) {
        List<RpSearchLog> rpSearchLogs = new ArrayList<>();
        
        try {
            log.info("【Bing SDK】开始下载报告，URL: {}", downloadUrl);
            
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(60000);
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            boolean isFirstLine = true;
            int lineCount = 0;
            
            while ((line = reader.readLine()) != null) {
                lineCount++;
                
                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("【Bing SDK】CSV标题行: {}", line);
                    continue;
                }
                
                // 解析CSV行
                RpSearchLog rpSearchLog = parseCsvLine(line, promotionType);
                if (rpSearchLog != null) {
                    rpSearchLogs.add(rpSearchLog);
                }
            }
            
            reader.close();
            connection.disconnect();
            
            log.info("【Bing SDK】报告下载完成，总行数: {}，解析记录数: {}", lineCount, rpSearchLogs.size());
            
        } catch (Exception e) {
            log.error("【Bing SDK】下载并解析报告失败", e);
        }
        
        return rpSearchLogs;
    }

    /**
     * 解析CSV行数据
     */
    private RpSearchLog parseCsvLine(String line, PromotionTypeEnum promotionType) {
        try {
            // 简单的CSV解析（实际项目中建议使用专业的CSV解析库）
            String[] fields = line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)");
            
            if (fields.length < 12) {
                log.warn("【Bing SDK】CSV行字段数不足: {}", line);
                return null;
            }
            
            RpSearchLog rpSearchLog = new RpSearchLog();
            
            // 解析各字段（根据实际CSV列顺序调整）
            rpSearchLog.setDayData(fields[0].replace("\"", ""));
            rpSearchLog.setTerm(fields[1].replace("\"", ""));
            rpSearchLog.setPlanScheme(fields[2].replace("\"", ""));
            rpSearchLog.setKeywordGroups(fields[3].replace("\"", ""));
            rpSearchLog.setKeyword(fields[4].replace("\"", ""));
            
            // 数值字段
            rpSearchLog.setImpressions(parseInteger(fields[5]));
            rpSearchLog.setClick(parseInteger(fields[6]));
            rpSearchLog.setConsumption(parseInteger(fields[7]));
            rpSearchLog.setClickRate(parseInteger(fields[8]));
            rpSearchLog.setAvgClickPrice(parseInteger(fields[9]));
            
            // 设备类型和匹配模式
            String deviceType = fields[10].replace("\"", "");
            rpSearchLog.setMatchMode(fields[11].replace("\"", ""));
            
            // 设置来源
            if ("Computer".equalsIgnoreCase(deviceType)) {
                rpSearchLog.setSources("BingPC端");
            } else if ("Smartphone".equalsIgnoreCase(deviceType) || "Tablet".equalsIgnoreCase(deviceType)) {
                rpSearchLog.setSources("Bing移动端");
            } else {
                rpSearchLog.setSources("Bing" + deviceType);
            }
            
            return rpSearchLog;
            
        } catch (Exception e) {
            log.warn("【Bing SDK】解析CSV行失败: {}", line, e);
            return null;
        }
    }

    /**
     * 安全解析整数
     */
    private Integer parseInteger(String value) {
        try {
            if (StringUtils.isBlank(value) || "\"\"".equals(value)) {
                return 0;
            }
            return Integer.parseInt(value.replace("\"", "").replace(",", ""));
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
