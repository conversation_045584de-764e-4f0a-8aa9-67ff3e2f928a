package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.report.promotion.baidu.BaiduAppService;
import com.tydic.nbchat.train.report.promotion.baidu.bo.BaiduCallbackBO;
import com.tydic.nbchat.train.report.promotion.bing.BingAppService;
import com.tydic.nbchat.train.report.promotion.bing.bo.BingCallbackBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/23 16:07
 * @description 开发者回调函数
 */
@Slf4j
@RestController
@RequestMapping("/train/callback")
public class AppCallbackController {

    @Resource
    private BaiduAppService baiduAppService;

    @Resource
    private BingAppService bingAppService;

    @GetMapping("/baidu")
    public String baiduCallBack(BaiduCallbackBO param) {
        return baiduAppService.callback(param);
    }

    @GetMapping("/bing")
    public String bingCallBack(BingCallbackBO param) {
        return bingAppService.callback(param);
    }
}
