package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.AppService;
import com.tydic.nbchat.train.report.promotion.baidu.BaiduAppService;
import com.tydic.nbchat.train.report.promotion.bing.BingAppService;
import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import com.tydic.nbchat.train.report.promotion.bo.AppServiceEnum;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import com.tydic.nbchat.train.report.promotion.e360.E360AppService;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description 推广平台OAuth授权管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/train/promotion")
public class PromotionOAuthController {

    @Resource
    private BingAppService bingAppService;

    @Resource
    private BaiduAppService baiduAppService;

    @Resource
    private E360AppService e360AppService;

    private final Map<String, AppService> appServiceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        appServiceMap.put(AppServiceEnum.BING.getCode(), bingAppService);
        appServiceMap.put(AppServiceEnum.BAIDU.getCode(), baiduAppService);
        appServiceMap.put(AppServiceEnum.E360.getCode(), e360AppService);
    }

    /**
     * 查找搜索日志记录
     *
     * @param appType 推广平台类型 (baidu/360/bing)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @param promotionType 推广类型 (computer/mobile，可选)
     * @param page 页码 (默认1)
     * @return 搜索日志记录列表
     */
    @GetMapping("/searchLog")
    public RspList<RpSearchLog> findRpSearchLog(
            @RequestParam String appType,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String promotionType,
            @RequestParam(defaultValue = "1") int page) {

        log.info("查找搜索日志记录：appType={}, startDate={}, endDate={}, promotionType={}, page={}",
                appType, startDate, endDate, promotionType, page);

        // 验证参数
        if (StringUtils.isBlank(appType)) {
            return BaseRspUtils.createErrorRspList("推广平台类型不能为空");
        }

        if (startDate == null || endDate == null) {
            return BaseRspUtils.createErrorRspList("开始日期和结束日期不能为空");
        }

        if (startDate.after(endDate)) {
            return BaseRspUtils.createErrorRspList("开始日期不能晚于结束日期");
        }

        // 获取对应的服务
        AppService appService = appServiceMap.get(appType.toLowerCase());
        if (appService == null) {
            return BaseRspUtils.createErrorRspList("不支持的推广平台类型: " + appType);
        }

        // 解析推广类型
        PromotionTypeEnum promotionTypeEnum = parsePromotionType(promotionType);

        try {
            return appService.findRpSearchLog(startDate, endDate, promotionTypeEnum, page);
        } catch (Exception e) {
            log.error("查找搜索日志记录失败", e);
            return BaseRspUtils.createErrorRspList("查找搜索日志记录失败: " + e.getMessage());
        }
    }

    /**
     * 解析推广类型
     */
    private PromotionTypeEnum parsePromotionType(String promotionType) {
        if (StringUtils.isBlank(promotionType)) {
            return PromotionTypeEnum.ALL_PROMOTION_TYPE;
        }

        switch (promotionType.toLowerCase()) {
            case "computer":
            case "pc":
                return PromotionTypeEnum.COMPUTER;
            case "mobile":
            case "smartphone":
                return PromotionTypeEnum.MOBILE;
            default:
                return PromotionTypeEnum.ALL_PROMOTION_TYPE;
        }
    }

    /**
     * 生成Bing OAuth授权URL
     *
     * @param customerId Bing Ads客户ID
     * @return 包含授权URL的响应
     */
    @GetMapping("/bing/authUrl")
    public Rsp<String> generateBingAuthUrl(@RequestParam String customerId) {
        log.info("生成Bing OAuth授权URL，customerId: {}", customerId);
        
        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }
        
        try {
            String authUrl = bingAppService.generateAuthUrl(customerId);
            if (StringUtils.isBlank(authUrl)) {
                return BaseRspUtils.createErrorRsp("生成授权URL失败，请检查customerId配置");
            }
            
            return BaseRspUtils.createSuccessRsp(authUrl);
            
        } catch (Exception e) {
            log.error("生成Bing OAuth授权URL异常", e);
            return BaseRspUtils.createErrorRsp("生成授权URL异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查Bing OAuth授权状态
     * 
     * @param customerId Bing Ads客户ID
     * @return 授权状态信息
     */
    @GetMapping("/bing/status")
    public Rsp<String> checkBingAuthStatus(@RequestParam String customerId) {
        log.info("检查Bing OAuth授权状态，customerId: {}", customerId);
        
        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }
        
        try {
            // 尝试获取访问令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isNotBlank(accessToken)) {
                return BaseRspUtils.createSuccessRsp("已授权");
            } else {
                return BaseRspUtils.createSuccessRsp("未授权");
            }
            
        } catch (Exception e) {
            log.error("检查Bing OAuth授权状态异常", e);
            return BaseRspUtils.createErrorRsp("检查授权状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 手动刷新Bing访问令牌
     *
     * @param customerId Bing Ads客户ID
     * @return 刷新结果
     */
    @PostMapping("/bing/refresh")
    public Rsp<String> refreshBingToken(@RequestParam String customerId) {
        log.info("手动刷新Bing访问令牌，customerId: {}", customerId);

        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }

        try {
            bingAppService.doRefreshToken(customerId);

            // 检查刷新后的令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isNotBlank(accessToken)) {
                return BaseRspUtils.createSuccessRsp("令牌刷新成功");
            } else {
                return BaseRspUtils.createErrorRsp("令牌刷新失败");
            }

        } catch (Exception e) {
            log.error("手动刷新Bing访问令牌异常", e);
            return BaseRspUtils.createErrorRsp("刷新令牌异常: " + e.getMessage());
        }
    }

    /**
     * 测试Bing API连接性
     *
     * @param customerId Bing Ads客户ID
     * @return 测试结果
     */
    @GetMapping("/bing/test")
    public Rsp<Map<String, Object>> testBingConnection(@RequestParam String customerId) {
        log.info("测试Bing API连接性，customerId: {}", customerId);

        Map<String, Object> result = new HashMap<>();

        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }

        try {
            // 检查配置
            result.put("configCheck", "开始检查配置...");

            // 检查访问令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isNotBlank(accessToken)) {
                result.put("tokenCheck", "访问令牌获取成功，长度: " + accessToken.length());
            } else {
                result.put("tokenCheck", "访问令牌获取失败");
                return BaseRspUtils.createSuccessRsp(result);
            }

            // 测试简单的搜索日志查询
            Date yesterday = DateTimeUtil.DateAdd(new Date(), -1,1);
            RspList<RpSearchLog> searchResult = bingAppService.findRpSearchLog(
                yesterday, yesterday, PromotionTypeEnum.ALL_PROMOTION_TYPE, 1);

            if (searchResult.isSuccess()) {
                result.put("apiTest", "API调用成功，返回数据条数: " + searchResult.getCount());
            } else {
                result.put("apiTest", "API调用失败: " + searchResult.getRspDesc());
            }

            return BaseRspUtils.createSuccessRsp(result);

        } catch (Exception e) {
            log.error("测试Bing API连接性异常", e);
            result.put("error", "测试异常: " + e.getMessage());
            return BaseRspUtils.createSuccessRsp(result);
        }
    }

    /**
     * 生成Bing SOAP请求示例（用于调试）
     *
     * @param customerId Bing Ads客户ID
     * @return SOAP请求示例
     */
    @GetMapping("/bing/soap-example")
    public Rsp<String> generateBingSoapExample(@RequestParam String customerId) {
        log.info("生成Bing SOAP请求示例，customerId: {}", customerId);

        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }

        try {
            // 获取应用配置
            ApplicationBO application = bingAppService.getAppByCustomerId(customerId);
            if (application == null) {
                return BaseRspUtils.createErrorRsp("未找到customerId对应的应用配置: " + customerId);
            }

            // 生成示例SOAP请求
            Date yesterday = DateTimeUtil.DateAdd(new Date(), -1,1);
            String soapRequest = bingAppService.buildSearchLogReportRequestForTest(
                application, yesterday, yesterday, PromotionTypeEnum.ALL_PROMOTION_TYPE, 0, 100);

            return BaseRspUtils.createSuccessRsp(soapRequest);

        } catch (Exception e) {
            log.error("生成Bing SOAP请求示例异常", e);
            return BaseRspUtils.createErrorRsp("生成SOAP请求示例异常: " + e.getMessage());
        }
    }

    /**
     * 测试Bing SDK调用
     *
     * @param customerId Bing Ads客户ID
     * @return 测试结果
     */
    @GetMapping("/bing/test-sdk")
    public Rsp<Map<String, Object>> testBingSdk(@RequestParam String customerId) {
        log.info("测试Bing SDK调用，customerId: {}", customerId);

        Map<String, Object> result = new HashMap<>();

        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }

        try {
            // 获取应用配置
            ApplicationBO application = bingAppService.getAppByCustomerId(customerId);
            if (application == null) {
                result.put("error", "未找到customerId对应的应用配置: " + customerId);
                return BaseRspUtils.createSuccessRsp(result);
            }

            result.put("configCheck", "应用配置检查通过");

            // 检查访问令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isBlank(accessToken)) {
                result.put("tokenCheck", "访问令牌获取失败");
                return BaseRspUtils.createSuccessRsp(result);
            }

            result.put("tokenCheck", "访问令牌获取成功，长度: " + accessToken.length());

            // 测试SDK调用
            Date yesterday = DateTimeUtil.DateAdd(new Date(), -1);
            List<RpSearchLog> searchLogs = bingAppService.getBingSdkService().findSearchLogWithSdk(
                application, accessToken, yesterday, yesterday, PromotionTypeEnum.ALL_PROMOTION_TYPE, 10);

            result.put("sdkTest", "SDK调用成功，返回记录数: " + searchLogs.size());

            return BaseRspUtils.createSuccessRsp(result);

        } catch (Exception e) {
            log.error("测试Bing SDK调用异常", e);
            result.put("error", "测试异常: " + e.getMessage());
            return BaseRspUtils.createSuccessRsp(result);
        }
    }
}
