# Bing Ads API SOAP Header 修复说明

## 问题描述

原始代码中的 SOAP Header 使用了过时的格式，导致 `InvalidCredentials (Code 105)` 错误。主要问题：

1. **使用了过时的 ApplicationToken**: 这是 10 年前旧版 API 的遗留字段
2. **缺少必填的鉴权字段**: Microsoft Advertising Reporting API v13 需要 4 个必填头元素
3. **缺少 CustomerAccountId**: 广告账户ID，与经理账号ID不同

## 修复内容

### 1. 修正 SOAP Header 格式

**修复前（错误格式）**:
```xml
<s:Header>
  <h:ApplicationToken xmlns:h="https://bingads.microsoft.com/Reporting/v13">
    YOUR_DEV_TOKEN
  </h:ApplicationToken>
  <h:CustomerId xmlns:h="https://bingads.microsoft.com/Reporting/v13">
    *********
  </h:CustomerId>
</s:Header>
```

**修复后（正确格式）**:
```xml
<s:Header xmlns:h="https://bingads.microsoft.com/Reporting/v13">
  <!-- 可选：操作名称 -->
  <h:Action s:mustUnderstand="1">SubmitGenerateReport</h:Action>
  
  <!-- 必填：开发者令牌 -->
  <h:DeveloperToken>YOUR_DEV_TOKEN</h:DeveloperToken>
  
  <!-- 必填：OAuth access_token -->
  <h:AuthenticationToken>YOUR_ACCESS_TOKEN</h:AuthenticationToken>
  
  <!-- 必填：CustomerId（经理账号ID） -->
  <h:CustomerId>*********</h:CustomerId>
  
  <!-- 必填：CustomerAccountId（广告账户ID） -->
  <h:CustomerAccountId>*********</h:CustomerAccountId>
</s:Header>
```

### 2. 修改的文件

#### 2.1 ApplicationBO.java
- 添加了 `customerAccountId` 字段
- 用于存储广告账户ID（与经理账号ID不同）

#### 2.2 BingAppService.java
- 修正了 `buildSearchLogReportRequest` 方法的 SOAP Header
- 添加了 `buildSearchLogReportRequestForTest` 测试方法
- 改进了错误处理和日志记录

#### 2.3 BingReportUtils.java
- 修正了 `buildPollGenerateReportRequest` 方法的 SOAP Header
- 更新了方法签名以接收更多必要参数

#### 2.4 PromotionOAuthController.java
- 添加了 `/train/oauth/bing/soap-example` 测试接口
- 用于生成和查看正确的 SOAP 请求格式

### 3. 配置更新

#### 3.1 必须添加 customerAccountId 配置
```yaml
nbchat-train:
  promotion:
    bing:
      appConfig:
        - customerId: "*********"                    # 经理账号ID
          developerToken: "1209Q0Z961153714"         # 开发者令牌
          clientId: "d5c38fb7-edd7-4793-a5fc-de9711aaf673"
          clientSecret: "****************************************"
          customerAccountId: "*********"             # 广告账户ID（新增必填）
          refreshToken: ""
          appName: "课件帮"
```

#### 3.2 配置说明
- **customerId**: 经理账号ID，用于标识管理账户
- **customerAccountId**: 广告账户ID，实际投放广告的账户ID
  - 可在 Bing Ads UI 左上角"帐号"列表中查看
  - 与 customerId 通常不同
  - **必须配置**，否则会出现权限不足错误

### 4. 其他改进

#### 4.1 列名修正
- 将 `MatchType` 改为 `DeliveredMatchType`（v13 API 正确列名）

#### 4.2 添加 Scope 限制
```xml
<Scope>
  <AccountIds xmlns:i="http://www.w3.org/2001/XMLSchema-instance" 
              i:type="tns:ArrayOflong" 
              xmlns:tns="https://bingads.microsoft.com/Reporting/v13">
    <long>*********</long>
  </AccountIds>
</Scope>
```

#### 4.3 改进错误处理
- 添加了详细的日志记录
- 改进了异常处理机制
- 增加了配置验证

### 5. 测试接口

#### 5.1 连接性测试
```
GET /train/oauth/bing/test?customerId=*********
```

#### 5.2 SOAP 请求示例生成
```
GET /train/oauth/bing/soap-example?customerId=*********
```

## 重要注意事项

1. **OAuth 令牌管理**: 
   - AuthenticationToken 必须是 ≤ 60 分钟的新 access_token
   - 系统会自动刷新过期令牌

2. **开发者令牌**: 
   - 推荐使用通用(Universal) Token
   - 要与生产/沙盒环境匹配

3. **账户ID 配置**: 
   - CustomerAccountId 是必填字段
   - 可以在 Bing Ads UI 中查看正确的账户ID

4. **权限验证**: 
   - 确保开发者令牌有访问指定账户的权限
   - 确保 OAuth 应用有正确的作用域

## 验证步骤

1. 更新配置文件，添加 `customerAccountId`
2. 调用 `/train/oauth/bing/test` 接口验证配置
3. 调用 `/train/oauth/bing/soap-example` 查看生成的 SOAP 请求
4. 测试实际的搜索日志查询功能

修复后应该不再出现 `InvalidCredentials (Code 105)` 错误。
