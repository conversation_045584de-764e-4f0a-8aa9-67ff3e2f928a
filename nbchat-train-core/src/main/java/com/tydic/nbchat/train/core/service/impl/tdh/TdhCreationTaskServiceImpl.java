package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.constants.RedisConstants;
import com.tydic.nbchat.train.api.bo.eums.ShareType;
import com.tydic.nbchat.train.api.bo.eums.TaskStateType;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskReqBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskRspBo;
import com.tydic.nbchat.train.api.tdh.TdhCreationTaskApi;
import com.tydic.nbchat.train.core.busi.TdhQueueCountBusiService;
import com.tydic.nbchat.train.core.config.UserConcurrencyConfig;
import com.tydic.nbchat.train.mapper.TdhCreationRecordMapper;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhCreationRecord;
import com.tydic.nbchat.train.mapper.po.TdhCreationTask;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.redis.RedisZsetHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description 我的作品
 * @Date 2023/8/29 17:55
 * @Created by dong_pc
 */
@Slf4j
@Service
public class TdhCreationTaskServiceImpl implements TdhCreationTaskApi {

    @Resource
    TdhCreationTaskMapper tdhCreationTaskMapper;
    @Resource
    TdhCreationRecordMapper tdhCreationRecordMapper;
    private final TdhQueueCountBusiService tdhQueueCountBusiService;
    private final RedisHelper redisHelper;
    private final RedisZsetHelper redisZsetHelper;

    private static final String process_format = "%.0f%%";
    public static final String TDH_MTK_QUEUE_KEY = "nbchat-tdh:task_queue_mtk";
    public static final String TDH_NONE_QUEUE_KEY = "nbchat-tdh:task_queue_none";
    public static final String TDH_IDLE_NONE_QUEUE_KEY = "nbchat-tdh:idle:task_queue_none";
    public static final String TDH_IDLE_MTK_QUEUE_KEY = "nbchat-tdh:idle:task_queue_none";


    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi userApi;
    @Autowired
    private UserConcurrencyConfig userConcurrencyConfig;

    public TdhCreationTaskServiceImpl(TdhQueueCountBusiService tdhQueueCountBusiService, RedisHelper redisHelper, RedisZsetHelper redisZsetHelper) {
        this.tdhQueueCountBusiService = tdhQueueCountBusiService;
        this.redisHelper = redisHelper;
        this.redisZsetHelper = redisZsetHelper;
    }

    @Override
    public RspList<TdhCreationTaskRspBo> getCreationList(TdhCreationTaskReqBo request) {
        TdhCreationTask cond = new TdhCreationTask();
        BeanUtils.copyProperties(request,cond);
        if (ShareType.DEFAULT_SHARE.getCode().equals(request.getIsShare())) {
            //查询我的作品 0
            cond.setIsShare(null);
        }
        if (ShareType.IS_SHARE.getCode().equals(request.getIsShare())) {
            //查询分享作品 1
            cond.setUserId(null);
        }
        if (ShareType.ID_SHARE.getCode().equals(request.getIsShare())) {
            //查询单个作品（通过ID）2
            cond.setUserId(null);
            cond.setIsShare(null);
        }
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        //获取排队任务
        //Map<String,Integer> queueMap = tdhQueueCountBusiService.getTaskQueueMap();
        if (StringUtils.isNoneBlank(request.getTargetTenant(),request.getTargetUid())) {
            cond.setUserId(request.getTargetUid());
            cond.setTenantCode(request.getTargetTenant());
        }
        Page<TdhCreationTask> info = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhCreationTaskMapper.selectAll(cond);
        List<TdhCreationTask> result = info.getResult();
        ArrayList<TdhCreationTaskRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result,rspList,TdhCreationTaskRspBo.class);
        for (TdhCreationTaskRspBo bo : rspList) {
            if (ObjectUtils.allNotNull(bo.getStartTime(),bo.getEndTime())) {
                bo.setDuration((bo.getEndTime().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (ObjectUtils.isEmpty(bo.getEndTime())) {
                bo.setDuration((new Date().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (TaskStateType.IN_THE_LINE.getCode().equals(bo.getTaskState())) {
                bo.setQueueNum(this.calQueueNum(bo));
            }
            this.calculateProcessRate(bo);
//            if (queueMap.containsKey(bo.getTaskId())) {
//                bo.setQueueNum(queueMap.get(bo.getTaskId()));
//            }
        }
        rspList.forEach(bo->{
            if (StringUtils.isNotEmpty(bo.getUserId())) {
                Rsp rsp = userApi.getUserInfo(bo.getUserId());
                if (rsp.isSuccess()) {
                    NbchatUserInfo nbchatUserInfo = (NbchatUserInfo) rsp.getData();
                    bo.setName(nbchatUserInfo.getName());
                }
                TdhCreationRecord obj = tdhCreationRecordMapper.querySampleById(bo.getCreationId(),"0");
                if (ObjectUtils.isNotEmpty(obj)) {
                    bo.setPreviewUrl(obj.getPreviewUrl());
                    bo.setCreationSource(obj.getCreationSource());
                }
            }
            if (StringUtils.isBlank(request.getTaskId())) {
                bo.setCreationContent(null);
            }
        });
        return BaseRspUtils.createSuccessRspList(rspList,info.getTotal());
    }

    public int calQueueNum(TdhCreationTaskRspBo bo) {
        String id = bo.getTaskId();
        String key = determineQueueKey(bo.getTdhType(), bo.getIdleQueue());
        Long rank = redisZsetHelper.zSetRank(key, id);

        if (rank == null || rank < 1L) {
            log.info("排队情况：{} | {} | {}", id, key, 1);
            return 1; // 如果 rank 为 null 或 0，直接返回 1
        }

        // 排除自己
        Set<String> preTasks = redisZsetHelper.zSetRange(key, 0L, rank - 1);
        AtomicInteger selfCount = new AtomicInteger(0);
        Map<String, Integer> otherCounts = countOtherUserTask(preTasks, bo.getUserId(), selfCount);

        // 计算队列序号
        int queueNum = preQueueNum(otherCounts) + selfCount.get() + 1;
        log.info("排队情况：{} | {} | {}", id, key, queueNum);
        return queueNum;
    }

    private String determineQueueKey(String taskType, String isIdleQueue) {
        // 根据任务类型和是否空闲队列标识，返回对应的队列键
        if ("1".equals(isIdleQueue)) {
            return "none".equals(taskType) ? TDH_IDLE_NONE_QUEUE_KEY : TDH_IDLE_MTK_QUEUE_KEY;
        } else {
            return "none".equals(taskType) ? TDH_NONE_QUEUE_KEY : TDH_MTK_QUEUE_KEY;
        }
    }

    //计算队列中用户任务数
    private Map<String, Integer> countOtherUserTask(Set<String> objects, String uId, AtomicInteger baseCount) {
        Map<String, Integer> userCountMap = new HashMap<>();

        for (String tid : objects) {
            String userId = (String) redisZsetHelper.hget("nbchat-tdh:task_info:" + tid, "user_id");
            if (uId.equals(userId)) {
                baseCount.set(baseCount.get() + 1);
                continue;
            }
            String userType = (String) redisZsetHelper.hget("nbchat-tdh:task_info:" + tid, "user_type");
            String key = userId + "-" + userType;
            userCountMap.put(key, userCountMap.getOrDefault(key, 0) + 1);
        }
        log.info("当前用户任务数：{} | 其他用户任务数：{}", baseCount.get() , userCountMap);
        return userCountMap;
    }

    private int preQueueNum(Map<String, Integer> userCountMap) {
        int queueNo = 0;
        Map<String, Integer> concurrencyMap = userConcurrencyConfig.getUserConcurrencyMap();
        for (Map.Entry<String, Integer> entry : userCountMap.entrySet()) {
            String userType = entry.getKey().split("-")[1];
            Integer userTaskCount = entry.getValue();
            queueNo += Math.min(userTaskCount, concurrencyMap.get(userType));
        }
        return queueNo;
    }

    private void calculateProcessRate(TdhCreationTaskRspBo bo) {
        if (bo.getPartCountTotal() > 1) {
            float partRate = 0.9f * bo.getPartCountDone() / bo.getPartCountTotal() * 100;
            bo.setProcessRate(String.format(process_format, partRate));

            String redisKey = RedisConstants.processKey(bo.getTaskId());
            if (redisHelper.hasKey(redisKey)) {
                String processRate = (String) redisHelper.get(redisKey);
                float concatRate = 0.1f * Float.parseFloat(processRate);
                bo.setProcessRate(String.format(process_format, partRate + concatRate));
            }

            if (bo.getPartCountDone() == 0) {
                bo.setProcessRate("1%");
            }
            if ("100%".equals(bo.getProcessRate()) && TaskStateType.MISSION_COMPLETED.getCode().equals(bo.getTaskState())) {
                bo.setProcessRate("98%");
            }
        } else {
            Integer esMakeDur = bo.getEstimatedMakeDuration();
            if (esMakeDur != null) {
                long makeDurSec = (new Date().getTime() - bo.getStartTime().getTime()) / 1000;
                if (makeDurSec > esMakeDur) {
                    bo.setProcessRate("98%");
                } else {
                    float partRate = (1.0f * makeDurSec / esMakeDur) * 100;
                    bo.setProcessRate(String.format(process_format, partRate));
                }
            }
        }
    }

    @Override
    public Rsp<TdhCreationTaskRspBo> getCreationInfo(TdhCreationTaskReqBo request) {
        TdhCreationTask task = tdhCreationTaskMapper.queryById(request.getTaskId());
        if (ObjectUtils.isNotEmpty(task)) {
            TdhCreationTaskRspBo rspBo = new TdhCreationTaskRspBo();
            BeanUtils.copyProperties(task,rspBo);
            return BaseRspUtils.createSuccessRsp(rspBo);
        }
        return BaseRspUtils.createErrorRsp("查询失败：任务不存在");
    }

    @Override
    public Rsp delete(TdhCreationTaskReqBo request) {
        log.info("数字人面板-删除作品:{}",request);
        if (StringUtils.isEmpty(request.getTaskId())) {
            return BaseRspUtils.createErrorRsp("作品id不得为空");
        }
        int i = tdhCreationTaskMapper.deleteById(request.getTaskId());
        return BaseRspUtils.createSuccessRsp(i);
    }

    @Override
    public Rsp update(TdhCreationTaskReqBo request) {
        log.info("数字人面板-更新作品:{}",request);
        if (StringUtils.isEmpty(request.getTaskId())) {
            return BaseRspUtils.createErrorRsp("作品id不得为空");
        }
        TdhCreationTask tdhCreationTask = new TdhCreationTask();
        BeanUtils.copyProperties(request,tdhCreationTask);
        tdhCreationTask.setUserId(null);
        int i = tdhCreationTaskMapper.update(tdhCreationTask);
        log.info("数字人面板-更新作品结果:{}",i);
        return BaseRspUtils.createSuccessRsp(i,"更新成功");
    }

    public void add(AtomicInteger var){
        var.set(var.get() + 10);
    }

    public static void main(String[] args) {
        TdhCreationTaskServiceImpl service = new TdhCreationTaskServiceImpl(null,null,null);
        AtomicInteger base = new AtomicInteger(1);
        service.add(base);
        System.out.println(base);
    }
}
