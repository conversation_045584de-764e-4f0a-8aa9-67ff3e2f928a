package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.user-concurrency-config")
public class UserConcurrencyConfig {

    /**
     * # 用户队列控制，配置用户角色对应最大任务数
     *   {
     *     '00': 1,  # 新用户
     *     '02': 1,  # 高级会员
     *     '03': 2,  # 专业会员
     *     '04': 2,  # 专业max会员
     *     '10': 1,  # 企业试用
     *     '11': 4,  # 企业正式
     * }
     */

    private Map<String, Integer> userConcurrencyMap = Map.of(
            "00", 1,
            "02", 1,
            "03", 2,
            "04", 2,
            "10", 2,
            "11", 4
    );



}
