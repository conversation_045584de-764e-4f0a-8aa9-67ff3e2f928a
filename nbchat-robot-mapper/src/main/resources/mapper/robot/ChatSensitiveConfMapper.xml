<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.ChatSensitiveConfMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.ChatSensitiveWordsConf" id="BaseResultMap">
        <result property="sensitiveId" column="sensitive_id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sensitiveFile" column="sensitive_file" jdbcType="VARCHAR"/>
        <result property="sensitiveState" column="sensitive_state" jdbcType="INTEGER"/>
        <result property="wordType" column="word_type" jdbcType="VARCHAR"/>
        <result property="loadType" column="load_type" jdbcType="VARCHAR"/>
        <result property="encrypt" column="encrypt" jdbcType="VARCHAR"/>
        <result property="split" column="split" jdbcType="VARCHAR"/>
        <result property="crtTime" column="crt_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap type="com.tydic.nbchat.robot.mapper.po.ChatSensitiveConf" id="BaseResultConfMap">
        <result property="confId" column="conf_id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="filterState" column="filter_state" jdbcType="INTEGER"/>
        <result property="filterType" column="filter_type" jdbcType="VARCHAR"/>
        <result property="crtTime" column="crt_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap type="com.tydic.nbchat.robot.mapper.po.ChatSensitiveWords" id="WordsResultMap">
        <result property="wordId" column="word_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sensitiveId" column="sensitive_id" jdbcType="INTEGER"/>
        <result property="wordType" column="word_type" jdbcType="VARCHAR"/>
        <result property="wordText" column="word_text" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="crtTime" column="crt_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="sensitive_words">
        word_id
        ,sensitive_id,tenant_code,word_type,word_text,crt_time,is_valid
    </sql>

    <sql id="config_words_col">
        sensitive_id
        ,tenant_code,sensitive_file,sensitive_state,word_type,load_type,crt_time,encrypt,split
    </sql>

    <sql id="config_col">
        conf_id
        ,tenant_code,filter_state,filter_type,crt_time
    </sql>


    <select id="getOneWord" resultMap="WordsResultMap">
        select <include refid="sensitive_words"/> from nbchat_sensitive_words
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and word_text = #{wordText,jdbcType=VARCHAR}
    </select>

    <select id="selectWordsListByCondition" resultMap="WordsResultMap">
        select
            <include refid="sensitive_words"/>
        from nbchat_sensitive_words
        <where>
            <if test="wordId != null">
                and word_id = #{wordId,jdbcType=VARCHAR}
            </if>
            <if test="sensitiveId != null">
                and sensitive_id = #{sensitiveId,jdbcType=INTEGER}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="wordType != null">
                and word_type = #{wordType,jdbcType=VARCHAR}
            </if>
            <if test="wordText != null and wordText != ''">
                and word_text like concat('%',#{wordText,jdbcType=VARCHAR},'%')
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updateWords" parameterType="com.tydic.nbchat.robot.mapper.po.ChatSensitiveWords">
        update nbchat_sensitive_words
        <set>
            <if test="wordText != null and wordText != ''">
                word_text = #{wordText,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=VARCHAR},
            </if>
            <if test="wordType != null">
                word_type = #{wordType,jdbcType=VARCHAR},
            </if>
            <if test="crtTime != null">
                crt_time = #{crtTime},
            </if>
        </set>
        where word_id = #{wordId,jdbcType=INTEGER}
    </update>


    <insert id="insertWords" parameterType="com.tydic.nbchat.robot.mapper.po.ChatSensitiveWords">
        insert into nbchat_sensitive_words
        (word_id, sensitive_id, tenant_code, word_type, word_text, crt_time, is_valid)
        values (#{wordId}, #{sensitiveId}, #{tenantCode}, #{wordType}, #{wordText}, #{crtTime}, #{isValid})
    </insert>

    <select id="selectWordsConfByCondition"
            parameterType="com.tydic.nbchat.robot.mapper.po.ChatSensitiveConfQueryCondition"
            resultMap="BaseResultMap">
        select
        <include refid="config_words_col"/>
        from
        nbchat_sensitive_words_conf
        <where>
            <if test="sensitiveId != null">
                and sensitive_id = #{sensitiveId}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode}
            </if>
            <if test="sensitiveState != null">
                and sensitive_state = #{sensitiveState}
            </if>
            <if test="loadType != null">
                and load_type = #{loadType}
            </if>
            <if test="wordType != null">
                and word_type = #{wordType}
            </if>
        </where>
    </select>

    <select id="selectConfigsByCondition"
            parameterType="com.tydic.nbchat.robot.mapper.po.ChatSensitiveConfQueryCondition"
            resultMap="BaseResultConfMap">
        select
        <include refid="config_col"/>
        from
        nbchat_sensitive_conf
        <where>
            <if test="sensitiveId != null">
                and conf_id = #{sensitiveId}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode}
            </if>
            <if test="filterState != null">
                and filter_state = #{filterState}
            </if>
            <if test="filterType != null">
                and filter_type = #{filterType}
            </if>
        </where>
    </select>

</mapper>

