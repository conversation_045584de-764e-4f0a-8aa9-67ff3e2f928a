package com.tydic.nicc.framework.utils;

import com.google.common.collect.Lists;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.eums.FileUploadType;
import com.tydic.nicc.dc.boot.starter.aws_s3.AwsS3Helper;
import com.tydic.nicc.dc.boot.starter.cos.CosHelper;
import com.tydic.nicc.dc.boot.starter.fastdfs.FastdfsHelper;
import com.tydic.nicc.dc.boot.starter.ftp.SFTPHelper;
import com.tydic.nicc.dc.boot.starter.minio.MinioHelper;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCompleteRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateResponse;
import com.tydic.nicc.dc.boot.starter.obs.ObsHelper;
import com.tydic.nicc.dc.boot.starter.oss.OssHelper;
import com.tydic.nicc.framework.config.FileManageConfigPropertiesBean;
import com.tydic.nicc.framework.exception.FileVerifyException;
import com.ykrenz.fastdfs.model.fdfs.StorePath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Classname FileManagerHelper
 * @Description 文件管理通用工具
 * @Date 2021/10/20 11:38 上午
 * @Created by kangkang
 */
@Slf4j
@Component
public class FileManagerHelper {

    @Autowired
    private ApplicationContext context;
    @Autowired(required = false)
    private OssHelper ossHelper;
    @Autowired(required = false)
    private SFTPHelper sftpHelper;
    @Autowired(required = false)
    private ObsHelper obsHelper;
    @Autowired(required = false)
    private FastdfsHelper fastdfsHelper;
    @Autowired(required = false)
    private MinioHelper minioHelper;
    @Autowired(required = false)
    private CosHelper cosHelper;
    @Autowired(required = false)
    private AwsS3Helper awsS3Helper;

    @Resource
    private FileManageConfigPropertiesBean fileManageConfigProperties;

    public FileManageConfigPropertiesBean getFileConfig() {
        return fileManageConfigProperties;
    }

    /**
     * 转换为 MultipartFile
     * @param fileName
     * @param data
     * @return
     */
    public static MultipartFile parseToMultipartFile(String fileName,byte[] data) {
        FileItem item = new DiskFileItem("files"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true, fileName, data.length, null);
        try {
            OutputStream os = item.getOutputStream();
            os.write(data);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new CommonsMultipartFile(item);
    }

    /**
     * file转MultipartFile
     * @param file
     * @return
     */
    public static MultipartFile parseToMultipartFile(File file) {
        FileItem item = new DiskFileItem("files"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName()
                , (int)file.length()
                , file.getParentFile());
        try {
            OutputStream os = item.getOutputStream();
            os.write(com.alibaba.excel.util.FileUtils.readFileToByteArray(file));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new CommonsMultipartFile(item);
    }


    /**
     * 保存文件
     * @param file
     * @param month
     * @return
     * @throws Exception
     */
    public List<FileManageSaveBO> saveFile(File file, String month) throws Exception {
        MultipartFile multipartFile = MultipartFileUtil.toMultipartFile(file);
        MultipartFile[] files = {multipartFile};
        return saveFiles(files,month,true);
    }

    /**
     * 保存文件
     * @param files
     * @param month
     * @return
     * @throws Exception
     */
    public List<FileManageSaveBO> saveFiles(MultipartFile[] files, String month) throws Exception {
        return saveFiles(files,month,true);
    }

    /**
     * 保存文件
     * @param files 文件
     * @param month 月份
     * @param compress 是否压缩
     * @return List<FileManageSaveBO>
     * @throws Exception 异常
     */
    public List<FileManageSaveBO> saveFiles(MultipartFile[] files, String month, boolean compress) throws Exception {
        return saveFiles(files,month,compress,false);
    }

    /**
     * 保存文件
     * @param files 文件
     * @param month 月份
     * @param compress 是否压缩
     * @param useOriginName 是否使用原始文件名
     * @return List<FileManageSaveBO>
     * @throws Exception 异常
     */
    public List<FileManageSaveBO> saveFiles(MultipartFile[] files, String month, boolean compress, boolean useOriginName) throws Exception {
        if(fileManageConfigProperties == null){
            log.error("文件上传-文件保存异常:请检查文件上传配置!");
            return new ArrayList<>();
        }
        List<FileManageSaveBO> fileSaveBOS = Lists.newArrayList();
        String uploadPath = "";
        if (FileUploadType.LOCAL.getCode().equals(fileManageConfigProperties.getUploadType())) {
            //上传到本地 nginx 目录
            uploadPath = fileManageConfigProperties.getLocalPath() + month;
            //创建目录
            File upDir = new File(uploadPath);
            if (!upDir.exists()) {
                upDir.mkdirs();
            }
        }
        if (FileUploadType.SFTP.getCode().equals(fileManageConfigProperties.getUploadType())) {
            //连接高版本ssh或jsch>0.1.55产生bug；每次获取实例重置连接
            sftpHelper = context.getBean(SFTPHelper.class);
            sftpHelper.mkdir(month);
        }
        for (MultipartFile file : files) {
            //校验文件合法性
            checkFileAllowed(file);
            // 创建文件id
            String fileNo = fileManageConfigProperties.createFileNo();
            // 获取上传文件的后缀
            String fileName = file.getOriginalFilename();
            //获取文件类型
            String fileType = "";
            if (fileName != null) {
                fileType = fileName.substring(fileName.lastIndexOf("."));
            }
            //绝对路径
            String absFilePath = uploadPath + File.separator + fileNo + fileType;
            //相对路径
            String newFileName;
            if (useOriginName) {
                //使用原始文件名
                newFileName = file.getOriginalFilename();
            } else {
                //使用新文件名
                newFileName = fileNo + fileType;
            }
            String relFilePath = month + File.separator + newFileName;
            FileManageSaveBO saveBO = new FileManageSaveBO();
            saveBO.setFileType(fileType);
            saveBO.setFileNo(fileNo);
            saveBO.setFileName(fileName);
            saveBO.setFilePath(relFilePath);
            saveBO.setAccessUrl(fileManageConfigProperties.getAccessUrlPrefix() + relFilePath);
            saveBO.setUploadType(fileManageConfigProperties.getUploadType());
            InputStream inputStream = file.getInputStream();
            try {
                long fileSize = file.getSize();
                if (compress && fileManageConfigProperties.getImageCompress().isCompress(file)) {
                    byte[] resultByte = ImageUtils.compressPicForScale(file.getBytes(), fileManageConfigProperties.getImageCompress().getDestSize());
                    inputStream = new ByteArrayInputStream(resultByte);
                    fileSize = resultByte.length;
                }
                saveBO.setFileSize(Math.toIntExact(fileSize));
                if (FileUploadType.LOCAL.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件保存到本地: fileName = {},path = {},size = {}", fileName, absFilePath, file.getSize());
                    if (compress && fileManageConfigProperties.getImageCompress().isCompress(file)) {
                        FileCopyUtils.copy(inputStream, new FileOutputStream(absFilePath));
                    } else {
                        file.transferTo(new File(absFilePath));
                    }
                }
                if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到minio: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    minioHelper.upload(relFilePath, inputStream);
                }
                if (FileUploadType.OSS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到oss: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    ossHelper.upload(relFilePath, inputStream);
                }
                if (FileUploadType.OBS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到obs: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    obsHelper.upload(relFilePath, inputStream);
                }
                if (FileUploadType.COS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到cos: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    cosHelper.upload(relFilePath, inputStream);
                }
                if (FileUploadType.AWS_S3.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到s3: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    awsS3Helper.upload(relFilePath, inputStream);
                }
                if (FileUploadType.SFTP.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到ftp: fileName = {},path = {},size = {}", fileName, relFilePath);
                    boolean upload = sftpHelper.uploadFile(inputStream, relFilePath);
                    if (!upload) {
                        log.error("文件上传-文件上传到sftp失败: relPath = {}", relFilePath);
                    }
                    sftpHelper.close();
                }
                if (FileUploadType.FDFS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                    log.info("文件上传-文件上传到fastdfs: fileName = {},path = {},size = {}", fileName, relFilePath, file.getSize());
                    StorePath storePath = fastdfsHelper.upload(file.getInputStream(), file.getSize(), fileType);
                    saveBO.setFilePath(storePath.getPath());
                    String accessUrl = fastdfsHelper.getAccessUrl(storePath.getPath());
                    saveBO.setAccessUrl(accessUrl);
                }
                fileSaveBOS.add(saveBO);
            } catch (Exception e) {
                log.error("文件保存异常:",e);
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.error("文件上传-文件流关闭异常:{}", e.getMessage());
                    }
                }
            }
        }
        log.info("文件上传-文件保存成功:{}", fileSaveBOS);
        return fileSaveBOS;
    }


    /**
     * 分片上传-创建任务
     * @param request
     * @return
     */
    public MultipartUploadCreateResponse createMultipartUpload(MultipartUploadCreateRequest request){
        MultipartUploadCreateResponse response = null;
        if(StringUtils.isBlank(request.getAccessUrlPrefix())){
            request.setAccessUrlPrefix(getFileConfig().getAccessUrlPrefix());
        }
        if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
            try {
                response = minioHelper.createMultipartUpload(request);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return response;
        } else {
            throw new RuntimeException("分片上传-创建任务-异常：当前仅支持mino!");
        }
    }

    /**
     * 分片上传-合并
     * @param request
     * @return
     */
    public boolean completeMultipartUpload(MultipartUploadCompleteRequest request){
        Assert.isNull(request.getObjectName(),"文件对象不得为空!");
        Assert.isNull(request.getUploadId(),"文件uploadId不得为空!");
        if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
            return minioHelper.completeMultipartUpload(request.getObjectName(),request.getUploadId());
        } else {
            throw new RuntimeException("分片上传-合并任务-异常：当前仅支持mino!");
        }
    }


    /**
     * 下载文件
     * @param fileName
     * @return
     */
    public InputStream downloadFile(String fileName) {
        InputStream is = null;
        try {
            if (FileUploadType.LOCAL.getCode().equals(fileManageConfigProperties.getUploadType())) {
                try {
                    is = new FileInputStream(fileName);
                } catch (FileNotFoundException e) {
                    throw new RuntimeException(e);
                }
            }
            if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = minioHelper.downloadFile(fileName);
            }
            if (FileUploadType.OSS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = ossHelper.downloadFile(fileName);
            }
            if (FileUploadType.OBS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = obsHelper.download(fileName).getObjectContent();
            }
            if (FileUploadType.COS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = cosHelper.download(fileName);
            }
            if (FileUploadType.AWS_S3.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = awsS3Helper.downloadFile(fileName);
            }
            if (FileUploadType.SFTP.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = sftpHelper.downloadFile(fileName);
            }
            if (FileUploadType.FDFS.getCode().equals(fileManageConfigProperties.getUploadType())) {
                is = fastdfsHelper.downloadFile(fileName);
            }
            return is;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除文件
     * @param filePath
     * @throws Exception
     */
    public void delete(String filePath) throws Exception {
        if (FileUploadType.LOCAL.getCode().equals(fileManageConfigProperties.getUploadType())) {
            File file = new File(filePath);
            if(file.exists()){
                file.delete();
            }
        }
        if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
            minioHelper.delete(filePath);
        }
        if (FileUploadType.OSS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            ossHelper.deleteFile(filePath);
        }
        if (FileUploadType.OBS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            obsHelper.delete(filePath);
        }
        if (FileUploadType.COS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            cosHelper.delete(filePath);
        }
        if (FileUploadType.AWS_S3.getCode().equals(fileManageConfigProperties.getUploadType())) {
            awsS3Helper.delete(filePath);
        }
        if (FileUploadType.SFTP.getCode().equals(fileManageConfigProperties.getUploadType())) {
            sftpHelper.deleteFile(filePath);
        }
        if (FileUploadType.FDFS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            fastdfsHelper.deleteFile(filePath);
        }
        if (FileUploadType.FDFS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            fastdfsHelper.deleteFile(filePath);
        }
    }


    /**
     * 校验文件合法性
     * @param file
     * @throws IOException
     */
    public void checkFileAllowed(MultipartFile file) throws IOException {
        //校验文件后缀，如果是图片，校验图片真实属性
        String fileName = file.getOriginalFilename();
        if(!checkConfigSuffix(fileName)){
            log.warn("文件上传-文件上传失败: filename = "+fileName);
            throw new FileVerifyException(fileManageConfigProperties.getErrorFileMsg());
        }
        if(ImageUtils.checkImageSuffix(fileName)){
            //图片类型校验
            if(!ImageUtils.isImage(file)){
                log.warn("文件上传-图片上传失败: filename = "+fileName);
                throw new FileVerifyException(fileManageConfigProperties.getErrorImgMsg());
            }
        } else {
            //其他文件类型校验
            if(fileManageConfigProperties.getFileCheckEnable()){
                //其他文件类型校验
                String headerType = FileUtils.getFileHeader(file.getInputStream());
                if(StringUtils.isBlank(FileUtils.getRealFileType(headerType))){
                    log.warn("文件上传-文件上传失败,文件类型非法: filename = " + fileName + " headType = " + headerType);
                    throw new FileVerifyException("文件上传失败,文件类型非法: " + fileName);
                }
            }
        }
    }

    /**
     * 校验文件后缀是否在白名单内
     * @param fileName
     * @return
     */
    public boolean checkConfigSuffix(String fileName){
        String suffix = fileManageConfigProperties.getSuffix();
        if(StringUtils.isEmpty(suffix) || suffix.equals("*")){
            return true;
        }
        String name = fileName.toLowerCase();
        String[] suffixs = suffix.split(",");
        for (String s : suffixs) {
            if(name.endsWith(s)){
                return true;
            }
        }
        log.warn("文件上传-文件后缀校验,只允许上传:{} 后缀的文件:{}",suffix,fileName);
        return false;
    }

    /**
     * 创建临时上传地址（使用nginx代理请去掉：#proxy_set_header HOST $host;）
     * @param objectName
     * @param queryParams
     * @return
     */
    public String getPrePutUrl(String objectName, Map<String, String> queryParams){
        String signUrl = "";
        if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = minioHelper.getPrePutUrl(objectName,queryParams);
        }
        if (FileUploadType.AWS_S3.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = awsS3Helper.getPrePutUrl(objectName,queryParams);
        }
        if (FileUploadType.OSS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = ossHelper.getPrePutUrl(objectName,queryParams);
        }
        return signUrl;
    }


    /**
     * 创建临时下载地址
     * @param objectName
     * @param queryParams
     * @return
     */
    public String getPreGetUrl(String objectName, Map<String, String> queryParams) {
        String signUrl = "";
        if (FileUploadType.MINIO.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = minioHelper.getPreGetUrl(objectName,queryParams);
        }
        if (FileUploadType.AWS_S3.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = awsS3Helper.getPreGetUrl(objectName,queryParams);
        }
        if (FileUploadType.OSS.getCode().equals(fileManageConfigProperties.getUploadType())) {
            signUrl = ossHelper.getPreGetUrl(objectName,queryParams);
        }
        return signUrl;
    }
}
