<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhCreationTaskMapper">
    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhCreationTask" id="TdhCreationTaskMap">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="sectionId" column="section_id" jdbcType="VARCHAR"/>
        <result property="creationId" column="creation_id" jdbcType="VARCHAR"/>
        <result property="creationName" column="creation_name" jdbcType="VARCHAR"/>
        <result property="creationContent" column="creation_content" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="queueTime" column="queue_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="partCountTotal" column="part_count_total" jdbcType="INTEGER"/>
        <result property="partCountDone" column="part_count_done" jdbcType="INTEGER"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="playUrl" column="play_url" jdbcType="VARCHAR"/>
        <result property="videoDuration" column="video_duration" jdbcType="VARCHAR"/>
        <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
        <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="isShare" column="is_share" jdbcType="VARCHAR"/>
        <result property="waterMark" column="water_mark" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="creationSource" column="creation_source" jdbcType="VARCHAR"/>
        <result property="creationType" column="creation_type" jdbcType="VARCHAR"/>
        <result property="previewUrl" column="preview_url" jdbcType="VARCHAR"/>
        <result property="idleQueue" column="idle_queue" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="tdhType" column="tdh_type" jdbcType="VARCHAR"/>
        <result property="estimatedMakeDuration" column="estimated_make_duration" jdbcType="INTEGER"/>
        <result property="estimatedVideoDuration" column="estimated_video_duration" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="videoListMap" type="com.tydic.nbchat.train.mapper.po.NbchatVideoDTO">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="sectionId" column="section_id" jdbcType="VARCHAR"/>
        <result property="creationId" column="creation_id" jdbcType="VARCHAR"/>
        <result property="creationName" column="creation_name" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="queueTime" column="queue_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="partCountTotal" column="part_count_total" jdbcType="INTEGER"/>
        <result property="partCountDone" column="part_count_done" jdbcType="INTEGER"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="playUrl" column="play_url" jdbcType="VARCHAR"/>
        <result property="videoDuration" column="video_duration" jdbcType="VARCHAR"/>
        <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
        <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="isShare" column="is_share" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName"/>
        <result column="creation_source" property="creationSource"/>
        <result column="creator_name" property="creatorName"/>
        <result column="phone" property="phone"/>
        <result column="preview_url" property="previewUrl"/>
        <result column="user_type" property="userType"/>
        <result column="vip_status" property="vipStatus"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="waterMark" column="water_mark" jdbcType="VARCHAR"/>
        <result property="idleQueue" column="idle_queue" jdbcType="VARCHAR"/>
        <result property="vipType" column="vip_type" jdbcType="VARCHAR"/>
        <result property="vipTypeSub" column="vip_type_sub" jdbcType="VARCHAR"/>
        <result property="vipUseDays" column="vip_use_days" jdbcType="INTEGER"/>
        <result property="registerDays" column="register_days" jdbcType="INTEGER"/>
        <result property="industry" column="industry" jdbcType="VARCHAR"/>
        <result property="estimatedMakeDuration" column="estimated_make_duration" jdbcType="INTEGER"/>
        <result property="estimatedVideoDuration" column="estimated_video_duration" jdbcType="INTEGER"/>
        <result property="downNum" column="down_num" jdbcType="INTEGER"/>
        <result property="shareNum" column="share_num" jdbcType="INTEGER"/>
        <result property="viewNum" column="view_num" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        task_id,
        user_id,
        tenant_code,
        course_id,
        section_id,
        creation_id,
        creation_name,
        creation_content,
        start_time,
        end_time,
        create_time,
        part_count_total,
        part_count_done,
        video_url,
        task_state,
        verify_state,
        play_url,
        user_type,
        tdh_type,
        idle_queue,
        error_code,
        error_desc,
        video_duration,
        queue_time,
        is_valid,
        is_share,
        water_mark,
        file_url,
        estimated_make_duration,
        estimated_video_duration
    </sql>

    <sql id="Base_Simple_Column_List">
        task_id,
        user_id,
        tenant_code,
        course_id,
        section_id,
        creation_id,
        creation_name,
        start_time,
        end_time,
        create_time,
        part_count_total,
        part_count_done,
        video_url,
        task_state,
        verify_state,
        play_url,
        user_type,
        idle_queue,
        error_code,
        error_desc,
        video_duration,
        queue_time,
        is_valid,
        is_share,
        water_mark,
        file_url
    </sql>

    <select id="selectSimpleByPrimaryKey" resultMap="TdhCreationTaskMap">
        select
        <include refid="Base_Simple_Column_List"/>
        from tdh_creation_task
        where task_id = #{taskId}
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhCreationTaskMap">
        select tct.task_id,
        tct.user_id,
        tct.tenant_code,
        tct.course_id,
        tct.section_id,
        tct.creation_id,
        tct.creation_name,
        tct.creation_content,
        tct.start_time,
        tct.end_time,
        tct.part_count_total,
        tct.part_count_done,
        tct.video_url,
        tct.task_state,
        tct.verify_state,
        tct.play_url,
        tcr.preview_url,
        tct.error_code,
        tct.error_desc,
        tct.video_duration,
        tct.queue_time,
        tct.is_valid,
        tct.is_share,
        tct.water_mark,
        tct.file_url,
        tcr.creation_source,
        tcr.creation_type
        from tdh_creation_task tct
        left join tdh_creation_record tcr on tct.creation_id = tcr.creation_id
        where tct.task_id = #{taskId}
    </select>

    <select id="selectQueueTasks" resultType="com.tydic.nbchat.train.mapper.po.TdhQueueInfo">
        select task_id as taskId,
        idle_queue as idleQueue,
        user_type as userType
        from tdh_creation_task where task_state = 'q'
        and is_valid = '1'
        <if test="startTime != null">
            and queue_time > #{startTime}
        </if>
        order by queue_time, start_time
    </select>


    <select id="selectAll" resultMap="TdhCreationTaskMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationTask">
        select
        <include refid="Base_Column_List"/>
        from tdh_creation_task
        <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="sectionId != null and sectionId != ''">
                and section_id = #{sectionId}
            </if>
            <if test="creationId != null and creationId != ''">
                and creation_id = #{creationId}
            </if>
            <if test="creationName != null and creationName != ''">
                and creation_name = #{creationName}
            </if>
            <if test="creationContent != null and creationContent != ''">
                and creation_content = #{creationContent}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="partCountTotal != null">
                and part_count_total = #{partCountTotal}
            </if>
            <if test="partCountDone != null">
                and part_count_done = #{partCountDone}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl}
            </if>
            <if test="taskState != null and taskState != ''">
                and task_state = #{taskState}
            </if>
            <if test="verifyState != null and verifyState != ''">
                and verify_state = #{verifyState}
            </if>
            <if test="errorCode != null and errorCode != ''">
                and error_code = #{errorCode}
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                and error_desc = #{errorDesc}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="isShare != null and isShare != ''">
                and is_share = #{isShare}
            </if>
            <if test="waterMark != null and waterMark != ''">
                and water_mark = #{waterMark}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
        </where>
        order by start_time desc
    </select>


    <insert id="insertSelective" keyProperty="taskId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationTask">
        insert into tdh_creation_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creationId != null and creationId != ''">
                creation_id,
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name,
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="partCountTotal != null">
                part_count_total,
            </if>
            <if test="partCountDone != null">
                part_count_done,
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url,
            </if>
            <if test="taskState != null and taskState != ''">
                task_state,
            </if>
            <if test="verifyState != null and verifyState != ''">
                verify_state,
            </if>
            <if test="errorCode != null and errorCode != ''">
                error_code,
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                error_desc,
            </if>
            <if test="waterMark != null and waterMark != ''">
                water_mark,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creationId != null and creationId != ''">
                #{creationId},
            </if>
            <if test="creationName != null and creationName != ''">
                #{creationName},
            </if>
            <if test="creationContent != null and creationContent != ''">
                #{creationContent},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="partCountTotal != null">
                #{partCountTotal},
            </if>
            <if test="partCountDone != null">
                #{partCountDone},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                #{videoUrl},
            </if>
            <if test="taskState != null and taskState != ''">
                #{taskState},
            </if>
            <if test="verifyState != null and verifyState != ''">
                #{verifyState},
            </if>
            <if test="errorCode != null and errorCode != ''">
                #{errorCode},
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                #{errorDesc},
            </if>
            <if test="waterMark != null and waterMark != ''">
                #{waterMark},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="taskId" useGeneratedKeys="true">
        insert into tdh_creation_task(creation_id, creation_name, creation_content, start_time, end_time,
        part_count_total, part_count_done, video_url, task_state, verify_state,
        error_code, error_desc)
        values (#{creationId}, #{creationName}, #{creationContent}, #{startTime}, #{endTime}, #{partCountTotal},
        #{partCountDone}, #{videoUrl}, #{taskState}, #{verifyState}, #{errorCode}, #{errorDesc})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_creation_task
        <set>
            <if test="creationId != null and creationId != ''">
                creation_id = #{creationId},
            </if>
            <if test="courseId != null">
                course_id = #{courseId},
            </if>
            <if test="sectionId != null">
                section_id = #{sectionId},
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name = #{creationName},
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content = #{creationContent},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="partCountTotal != null">
                part_count_total = #{partCountTotal},
            </if>
            <if test="partCountDone != null">
                part_count_done = #{partCountDone},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url = #{videoUrl},
            </if>
            <if test="taskState != null and taskState != ''">
                task_state = #{taskState},
            </if>
            <if test="verifyState != null and verifyState != ''">
                verify_state = #{verifyState},
            </if>
            <if test="errorCode != null and errorCode != ''">
                error_code = #{errorCode},
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                error_desc = #{errorDesc},
            </if>
            <if test="isShare != null and isShare != ''">
                is_share = #{isShare},
            </if>
            <if test="waterMark != null and waterMark != ''">
                water_mark = #{waterMark},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
        </set>
        where task_id = #{taskId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update tdh_creation_task
        set is_valid = '0'
        where task_id = #{taskId}
    </delete>


    <select id="getVideoCount" resultType="int">
        SELECT COUNT(*)
        FROM tdh_creation_task
        <where>
            <choose>
                <!-- 如果没有传入参数或传入0，查询当天的数据 -->
                <when test="timeRange == 0 or timeRange == null">
                    AND DATE(create_time) = CURDATE()
                </when>
                <!-- 如果传入了数字（29或6等），查询前一天开始，直到这个数字之前的数据 -->
                <when test="timeRange != 0 and timeRange != null">
                    AND create_time >= DATE_SUB(CURDATE(), INTERVAL #{timeRange} DAY)
                    AND create_time
                    &lt; DATE_SUB(CURDATE()
                    , INTERVAL 0 DAY)
                </when>
                <!-- 默认情况下查询当天的数据 -->
                <otherwise>
                    AND DATE (create_time) = CURDATE()
                </otherwise>
            </choose>
            <choose>
                <when test="userType != null and userType == 0">
                    AND tenant_code = '00000000'
                </when>
                <when test="userType == null or userType == ''">
                    <!-- 不添加任何 SQL -->
                </when>
                <otherwise>
                    AND tenant_code != '00000000'
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getVideoList" resultMap="videoListMap">
        SELECT c.creation_id,
        c.creation_source,
        c.preview_url,
        u.user_name AS creator_name,
        u.phone AS phone,
        u.user_type,
        u.vip_status,
        u.company_name,
        v.task_id,
        v.user_id,
        v.tenant_code,
        v.course_id,
        v.section_id,
        v.creation_id,
        v.creation_name,
        v.start_time,
        v.end_time,
        v.part_count_total,
        v.part_count_done,
        v.video_url,
        v.task_state,
        v.verify_state,
        v.play_url,
        v.error_code,
        v.error_desc,
        v.video_duration,
        v.queue_time,
        v.is_valid,
        v.is_share,
        v.water_mark,
        v.file_url,
        v.idle_queue,
        v.create_time,
        v.estimated_make_duration,
        v.estimated_video_duration,
        t2.vip_type,
        t2.vip_type_sub,
        t2.vip_use_days,
        t2.register_days,
        <if test="industry != null and industry != ''">
            t3.item_value as industry,
        </if>
        ifnull(suol.down_num, 0) AS down_num,
        ifnull(suol.share_num, 0) AS share_num,
        ifnull(suol.view_num, 0) AS view_num
        FROM tdh_creation_task v
        LEFT JOIN tdh_creation_analysis t2 ON t2.task_id = v.task_id
        LEFT JOIN tdh_creation_record c ON c.creation_id = v.creation_id
        LEFT JOIN op_rp_user_detail u ON v.user_id = u.user_id AND v.tenant_code = u.tenant_code
        LEFT JOIN (SELECT busi_id,
        sum(if(type = 'video_down', 1, 0)) AS down_num,
        sum(if(type = 'video_share', 1, 0)) AS share_num,
        sum(if(type IN ('video_web_view', 'video_mini_view'), 1, 0)) AS view_num
        FROM sys_user_operate_log
        WHERE type IN ('video_down', 'video_share', 'video_web_view', 'video_mini_view')
        GROUP BY busi_id) suol ON suol.busi_id = v.task_id
        <if test="industry != null and industry != ''">
            LEFT JOIN rp_user_questionnaire t3 ON v.user_id = t3.user_id
        </if>
        <where>
            <if test="industry != null and industry != ''">
                AND t3.item_value = #{industry} and v.tenant_code = t3.tenant_code
            </if>
            <!-- 视频类型 -->
            <if test="creationSource != null and creationSource != ''">
                AND c.creation_source = #{creationSource}
            </if>
            <!-- 制作时长 -->
            <if test="minVideoDuration != null">
                AND v.video_duration >= #{minVideoDuration}
            </if>
            <if test="maxVideoDuration != null">
                AND v.video_duration &lt;= #{maxVideoDuration}
            </if>
            <!-- 内容分类 -->
            <if test="creationType != null and creationType != ''">
                AND c.creation_type = #{creationType}
            </if>
            <!-- 付费状态 -->
            <if test="isPay == '1'.toString()">
                and u.total_pay_amount > 0
            </if>
            <if test="isPay == '0'.toString()">
                and u.total_pay_amount = 0
            </if>
            <!-- 用户属性 -->
            <if test="userType != null and userType != ''">
                AND u.user_type = #{userType}
            </if>
            <!-- 企业名称 -->
            <if test="companyName != null and companyName != ''">
                AND u.company_name LIKE CONCAT('%', #{companyName}, '%')
            </if>
            <if test="targetTenantCode != null and targetTenantCode != ''">
                AND v.tenant_code = #{targetTenantCode}
            </if>
            <!-- 时间范围 -->
            <if test="startTime != null">
                AND v.start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND v.start_time &lt;= #{endTime}
            </if>
            <if test="createTimeBegin != null and createTimeEnd != null">
                AND v.create_time between #{createTimeBegin} and #{createTimeEnd}
            </if>
            <!-- 视频状态 -->
            <if test="taskStateList != null and !taskStateList.isEmpty()">
                AND v.task_state IN
                <foreach item="item" collection="taskStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="idleQueue != null and idleQueue != ''">
                AND v.idle_queue = #{idleQueue}
            </if>
            <if test="isValid != null and isValid != ''">
                AND v.is_valid = #{isValid}
            </if>
            <!-- 手机号 -->
            <if test="phone != null and phone != ''">
                AND u.phone = #{phone}
            </if>
            <!-- 内容搜索 -->
            <if test="keyword != null and keyword != ''">
                AND (instr(v.creation_name, #{keyword}) OR instr(v.creation_content, #{keyword}) )
            </if>
            <!-- 作品id -->
            <if test="creationId != null and creationId != ''">
                AND v.creation_id = #{creationId}
            </if>
            <!-- 任务id -->
            <if test="taskId != null and taskId != ''">
                AND v.task_id = #{taskId}
            </if>
            <!-- 用户id -->
            <if test="targetUserId != null and targetUserId != ''">
                AND v.user_id = #{targetUserId}
            </if>
            <!-- 会员类型 -->
            <if test="vipTypes != null and vipTypes.size() > 0">
                and (
                <foreach collection="vipTypes" separator=" OR " index="index" item="item" open="(" close=")">
                    (t2.vip_type = #{item.vipType} AND t2.vip_type_sub = #{item.vipTypeSub})
                </foreach>
                )
            </if>
            <!-- 视频标签（双语字幕、智能进度条、片头片尾、文字模版、定制声音、定制形象、板书标记、字幕标记、上传视频、数字人镜头） -->
            <if test="subtitleBilingual != null">
                AND t2.subtitle_bilingual = #{subtitleBilingual}
            </if>
            <if test="videoBar != null">
                AND t2.video_bar = #{videoBar}
            </if>
            <if test="opId != null">
                AND LENGTH(t2.op_id)
                <choose>
                    <when test="opId == 0">
                        = 0
                    </when>
                    <when test="opId == 1">
                        > 0
                    </when>
                </choose>
            </if>
            <if test="edId != null">
                AND LENGTH(t2.ed_id)
                <choose>
                    <when test="edId == 0">
                        = 0
                    </when>
                    <when test="edId == 1">
                        > 0
                    </when>
                </choose>
            </if>
            <if test="pagMap != null">
                AND t2.pag_map = #{pagMap}
            </if>
            <if test="customVoice != null and customVoice == 1">
                <!-- 判断有无定制声音的订单Id -->
                AND LENGTH(t2.custom_voice) > 0
            </if>
            <if test="customTdh != null and customTdh == 1">
                <!-- 判断有无定制形象的订单Id -->
                AND LENGTH(t2.custom_tdh) > 0
            </if>
            <if test="banshuMark != null">
                AND t2.banshu_mark = #{banshuMark}
            </if>
            <if test="subtitleMark != null">
                AND t2.subtitle_mark = #{subtitleMark}
            </if>
            <if test="pipUpload != null">
                AND t2.pip_upload = #{pipUpload}
            </if>
            <if test="tdhCrop != null">
                AND t2.tdh_crop = #{tdhCrop}
            </if>
        </where>
        ORDER BY v.start_time DESC
    </select>
    <select id="selectTdhForAnalysis" resultType="com.tydic.nbchat.train.mapper.po.TdhAnalysisDTO">
        <![CDATA[
        SELECT tct.task_id                                                                                                                                                                 AS taskId,
               left(regexp_replace(replace(replace(replace(replace(concat(creation_content -> '$.parts[*].content'), '", "', ''), '["', ''), '"]', ''), '\\"', '"'), '<[^>]+>', ''), 5000) AS oralDraft
        FROM tdh_creation_task tct
                 LEFT JOIN tdh_creation_analysis tca
                           ON tct.task_id = tca.task_id
        WHERE tca.analysis_type = '0'
          AND tca.video_class IS NULL
          AND tct.task_state = '1'
        LIMIT 20 OFFSET #{offset}
        ]]>
    </select>

    <select id="queryRankNo" resultType="Integer">
        with task_rank as (select
        task_id,
        rank() over (order by queue_time) as rank_no
        from tdh_creation_task
        where task_state = 'q'
        and is_valid = '1'
        and queue_time > date_add(now(), interval -2 day)
        order by queue_time)
        select rank_no from task_rank where task_id = #{taskId}
    </select>

    <select id="queueInfo" resultType="com.tydic.nbchat.train.mapper.po.QueueInfo">
        with t_queue_time as (select queue_time from tdh_creation_task where task_id = #{taskId}),
             t_queue as (select tenant_code, task_id, user_id
                         from tdh_creation_task
                         where is_valid = '1'
                           and task_state = 'q'
                           and queue_time >= (select queue_time from t_queue_time)),
             task_user as (select tenant_code, user_id, count(1) as task_count
                           from t_queue
                           group by tenant_code, user_id),
             tenant_info as (select tenant_code, status
                             from nbchat_sys_tenant
                             where tenant_code in (select distinct tenant_code from task_user)),
             user_vip as (select tenant_code, user_id, max(vip_type) as vip_type
                          from nbchat_user_vip
                          where vip_status = '1'
                            and user_id in (select user_id from task_user)
                          group by user_id)
        select t1.tenant_code   as tenantCode,
               t1.user_id       as userId,
               t1.task_count    as taskCount,
               t2.vip_type      as vipType,
               t3.status        as tenantStatus
        from task_user t1
                 left join user_vip t2 on t1.user_id = t2.user_id and t1.tenant_code = t2.tenant_code
                 left join tenant_info t3 on t1.tenant_code = t3.tenant_code
    </select>
</mapper>
