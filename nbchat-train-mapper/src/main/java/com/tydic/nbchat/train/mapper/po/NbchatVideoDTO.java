package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NbchatVideoDTO implements Serializable {

    private Integer estimatedMakeDuration;  //预估制作时长
    private Integer estimatedVideoDuration; //预估视频时长
    private String industry;

    private String vipType;
    private String vipTypeSub;
    private Integer vipUseDays;  //开通会员时间-创作时间
    private Integer registerDays;//注册时间-创作时间

    private Date queueTime; //进入排队时间
    private String isValid;

    private String userId;
    private String tenantCode;
    private String courseId;
    private String sectionId;
    private String videoDuration;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 创作id
     */
    private String creationId;

    private String creationName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    private Date createTime;
    /**
     * 总视频段
     */
    private Integer partCountTotal;
    /**
     * 完成视频段
     */
    private Integer partCountDone;
    /**
     * 视频下载地址
     */
    private String videoUrl;
    private String playUrl;
    /**
     * 0 生成中  1 任务完成 2 任务异常
     */
    private String taskState;
    /**
     * 0 待审核 1 审核通过 2 审核不通过
     */
    private String verifyState;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 0个人 1已共享
     */
    private String isShare;
    /**
     * 视频类型;1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 内容分类;1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    /**
     * 付费状态
     */
    private String vipStatus;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 创作者名称
     */
    private String creatorName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 缩略图
     */
    private String previewUrl;
    /**
     * 原始文件地址
     */
    private String fileUrl;
    /**
     * 水印;0-无/1-有
     */
    private String waterMark;
    private String idleQueue;

    /**
     * 表示视频下载的次数。
     * 此值跟踪与视频相关的下载计数，提供对其使用情况和受欢迎程度的洞察。
     */
    private Integer downNum;
    /**
     * 表示视频被共享的次数。
     * 该值跟踪与视频相关的共享活动，提供对其分布和覆盖范围的见解。
     */
    private Integer shareNum;
    /**
     * 表示视频的观看次数。
     * 此值跟踪与视频相关的观看次数，提供对其受欢迎程度和覆盖范围的洞察。
     */
    private Integer viewNum;
}
