package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (PptCreationRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-02 17:23:48
 */
public interface PptCreationRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param pptId 主键
     * @return 实例对象
     */
    PptCreationRecord queryById(String pptId);

    List<PptCreationRecord> selectAll(PptCreationRecord pptCreationRecord);

    int insertSelective(PptCreationRecord pptCreationRecord);

      /**
     * 修改数据
     *
     * @param pptCreationRecord 实例对象
     * @return 影响行数
     */
    int update(PptCreationRecord pptCreationRecord);

    /**
     * 通过主键删除数据
     *
     * @param pptId 主键
     * @return 影响行数
     */
    int deleteById(String pptId);

    /**
     * 查询视频制作数量（按用户类型区分）
     * @param timeRange 时间范围
     * @param userType 用户类型 0-个人用户 1-企业用户
     * @return 数量
     */
    int getPptCount(@Param("timeRange") int timeRange, @Param("userType") String userType);

    /**
     * 查询视频列表
     * @param condition
     * @return
     */
    List<NbchatPptDTO> getPptList(NbchatPptCondition condition);
}

