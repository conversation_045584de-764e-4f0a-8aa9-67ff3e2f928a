package com.tydic.nbchat.admin.report.web;

import com.tydic.nbchat.admin.api.bo.rp.UserOperateInfoBO;
import com.tydic.nbchat.admin.api.rp.UserOperateApi;
import com.tydic.nbchat.admin.core.utils.IPUtils;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static com.tydic.nicc.common.nbchat.emus.UserOperationType.PAGE;

/**
 * <AUTHOR>
 * @date 2025/3/25 10:36
 * @description 用户操作记录控制器，提供用户操作记录相关的管理接口。
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/user/operate")
public class UserOperateController {

    private final UserOperateApi userOperateApi;

    /**
     * 保存用户操作记录。
     * 该方法从请求头中获取客户端的User-Agent信息，并将其设置到用户操作信息参数中，
     * 然后调用用户操作API完成保存操作。
     *
     * @param request HTTP请求对象，用于获取请求头中的User-Agent信息
     * @param param   用户操作信息，包括租户ID、用户ID、操作类型、业务ID和操作内容等
     * @return 操作结果响应对象，包含操作状态及相关消息
     */
    @PostMapping("/save")
    public Rsp<?> save(@RequestBody UserOperateInfoBO param, HttpServletRequest request) {
        if (StringUtils.isBlank(param.getType())) {
            param.setType(PAGE.getType());
        }
        param.setClientUa(request.getHeader("User-Agent"));
        param.setClientIp(IPUtils.getIpAddr(request));
        return userOperateApi.save(param);
    }
}
