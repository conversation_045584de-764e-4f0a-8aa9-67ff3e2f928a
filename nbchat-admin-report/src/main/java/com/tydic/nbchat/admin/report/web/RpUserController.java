package com.tydic.nbchat.admin.report.web;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.rp.*;
import com.tydic.nbchat.admin.api.rp.RpRevenueDataService;
import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nbchat.admin.api.rp.RpUserGrowService;
import com.tydic.nbchat.admin.api.rp.UserRpApi;
import com.tydic.nbchat.admin.mapper.RpHourVideoCountMapper;
import com.tydic.nbchat.admin.mapper.RpUserPaymentInfoMapper;
import com.tydic.nbchat.admin.mapper.RpUserPptVideoInfoMapper;
import com.tydic.nbchat.admin.mapper.po.RpHourVideoCount;
import com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo;
import com.tydic.nbchat.admin.mapper.po.RpUserPptVideoInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/rp/")
public class RpUserController {

    private final UserRpApi userRpApi;
    private final RpUserGrowService rpUserGrowService;
    private final RpUserDetailService rpUserDetailService;
    private final RpRevenueDataService rpRevenueDataService;
    private final RpHourVideoCountMapper rpHourVideoCountMapper;
    private final RpUserPaymentInfoMapper rpUserPaymentInfoMapper;
    private final RpUserPptVideoInfoMapper rpUserPptVideoInfoMapper;

    @PostMapping("/user/list")
    public RspList<RpUserDetailBO> getRpUserDetailList(@Valid @RequestBody RpUserDetailQueryReqBO reqBo) {
        return rpUserDetailService.getRpUserDetailList(reqBo);
    }

    @PostMapping("/user/export")
    public Rsp exportRpUserDetailList(@Valid @RequestBody RpUserDetailQueryReqBO reqBo) {
        return rpUserDetailService.exportRpUserDetailList(reqBo);
    }

    @PostMapping("/user/grow")
    public Rsp userGrow(@RequestBody RpUserGrowQueryReqBO reqBo) {
        return rpUserGrowService.userGrowUp(reqBo.getStartTime(), reqBo.getEndTime());
    }

    @PostMapping("/revenue")
    public Rsp getRpRevenueData(@RequestBody RpUserGrowQueryReqBO reqBo) {
        return rpRevenueDataService.getRpRevenueData(reqBo.getStartTime(), reqBo.getEndTime());
    }

    @Transactional
    @PostMapping("/manualUpdate/select/{type}")
    public Rsp manualUpdateRp(@PathVariable("type") String type) {
        log.info("手动触发用户维度报表刷新，接口开始");
        switch (type) {
            case "1":
                userRpApi.manualUpdateRpUserPromotionInfo();
                break;
            case "2":
                userRpApi.manualUpdateRpUserTdhInfo();
                break;
            case "3":
                userRpApi.manualUpdateRpUserPayInvoiceInfo();
                break;
            case "4":
                userRpApi.manualUpdateRpUserActivityInfo();
                break;
            case "5":
                userRpApi.manualUpdateRpUserExamInfo();
                break;
            case "6":
                rpUserPaymentInfoMapper.deleteAll();
                break;
            case "60":
                insertOrUpdateScoreBalance();
                break;
            case "61":
                insertOrUpdateScoreDetail();
                break;
            case "62":
                insertOrUpdatePayInfo();
                break;
            case "63":
                insertOrUpdateRefund();
                break;
            case "7":
                rpUserPptVideoInfoMapper.deleteAll();
                break;
            case "70":
                insertOrUpdateDownload();
                break;
            case "71":
                insertOrUpdatePptMake();
                break;
            case "72":
                insertOrUpdateTdh1();
            case "73":
                insertOrUpdateTdh2();
                break;
        }
        log.info("手动触发用户维度报表刷新，接口结束");
        return BaseRspUtils.createSuccessRsp("报表数据刷新成功");
    }

    private void insertOrUpdateScoreBalance() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPaymentInfoBO> list;
            try (Page<RpUserPaymentInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPaymentInfoMapper.selectScoreBalance();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("可用算力点数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPaymentInfoBO.class);
            }
            userRpApi.manualUpdateRpUserPaymentInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdateScoreDetail() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPaymentInfoBO> list;
            try (Page<RpUserPaymentInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPaymentInfoMapper.selectScoreDetail();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("算力点消费数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPaymentInfoBO.class);
            }
            userRpApi.manualUpdateRpUserPaymentInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdatePayInfo() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPaymentInfoBO> list;
            try (Page<RpUserPaymentInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPaymentInfoMapper.selectPayInfo();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("支付数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPaymentInfoBO.class);
            }
            userRpApi.manualUpdateRpUserPaymentInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdateRefund() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPaymentInfoBO> list;
            try (Page<RpUserPaymentInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPaymentInfoMapper.selectRefund();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("退款数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPaymentInfoBO.class);
            }
            userRpApi.manualUpdateRpUserPaymentInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdatePptMake() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPptVideoInfoBO> list;
            try (Page<RpUserPptVideoInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPptVideoInfoMapper.selectAllPptMake();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("PPT制作数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPptVideoInfoBO.class);
            }
            userRpApi.manualUpdateRpPptVideoInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdateTdh1() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPptVideoInfoBO> list;
            try (Page<RpUserPptVideoInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPptVideoInfoMapper.selectAllTdh1();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("视频制作数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPptVideoInfoBO.class);
            }
            userRpApi.manualUpdateRpPptVideoInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdateTdh2() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPptVideoInfoBO> list;
            try (Page<RpUserPptVideoInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPptVideoInfoMapper.selectAllTdh2();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("视频制作数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPptVideoInfoBO.class);
            }
            userRpApi.manualUpdateRpPptVideoInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdateDownload() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpUserPptVideoInfoBO> list;
            try (Page<RpUserPptVideoInfo> page = PageHelper.startPage(pageNum, 500)) {
                rpUserPptVideoInfoMapper.selectAllDownload();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("导出数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpUserPptVideoInfoBO.class);
            }
            userRpApi.manualUpdateRpPptVideoInfo(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    @Transactional
    @PostMapping("/manualUpdate/hour")
    public Rsp manualUpdateRpHour() {
        log.info("手动触发用户维度报表刷新，接口开始");
        rpHourVideoCountMapper.deleteAll();
        insertOrUpdateVideoHour();
        insertOrUpdatePptHour();
        log.info("手动触发用户维度报表刷新，接口结束");
        return BaseRspUtils.createSuccessRsp("报表数据刷新成功");
    }

    private void insertOrUpdateVideoHour() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpHourVideoCountBO> list;
            try (Page<RpHourVideoCount> page = PageHelper.startPage(pageNum, 500)) {
                rpHourVideoCountMapper.selectAllVideo();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("视频制作历史数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpHourVideoCountBO.class);
            }
            userRpApi.manualUpdateRpHour(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }

    private void insertOrUpdatePptHour() {
        int pageNum = 1;
        int pageCount = -1;
        do {
            List<RpHourVideoCountBO> list;
            try (Page<RpHourVideoCount> page = PageHelper.startPage(pageNum, 500)) {
                rpHourVideoCountMapper.selectAllPpt();
                if (pageCount < 0) {
                    pageCount = page.getPages();
                    log.info("PPT制作历史数据存在{}条", page.getTotal());
                }
                list = new ArrayList<>();
                NiccCommonUtil.copyList(page.getResult(), list, RpHourVideoCountBO.class);
            }
            userRpApi.manualUpdateRpHour(list);
            pageNum += 1;
        } while (pageNum <= pageCount);
    }
}
