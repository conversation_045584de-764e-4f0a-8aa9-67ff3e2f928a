package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.bo.menu.*;
import com.tydic.nbchat.admin.api.menu.SysMenuTemplateApi;
import com.tydic.nbchat.admin.core.busi.SysMenuBusiService;
import com.tydic.nbchat.admin.mapper.SysMenuTplMapper;
import com.tydic.nbchat.admin.mapper.SysMenuTplRelMenuMapper;
import com.tydic.nbchat.admin.mapper.po.SysMenuTpl;
import com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SysMenuTemplateServiceImpl implements SysMenuTemplateApi {

    @Resource
    private SysMenuTplMapper sysMenuTplMapper;
    @Resource
    private SysMenuTplRelMenuMapper sysMenuTplRelMenuMapper;

    final private SysMenuBusiService sysMenuBusiService;

    public SysMenuTemplateServiceImpl(SysMenuBusiService sysMenuBusiService) {
        this.sysMenuBusiService = sysMenuBusiService;
    }

    @Override
    public RspList<SysMenuTplBO> getTemplates(SysMenuTplQueryReqBO reqBO) {
        log.info("获取菜单模板列表:{}", reqBO);
        SysMenuTpl record = new SysMenuTpl();
        record.setTplCode(reqBO.getTplCode());
        record.setTplName(reqBO.getTplName());
        record.setTplType(reqBO.getTplType());
        record.setIsValid(EntityValidType.NORMAL.getCode());
        Page<SysMenuTpl> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        sysMenuTplMapper.selectByCondition(record);
        List<SysMenuTplBO> list = Lists.newArrayList();
        NiccCommonUtil.copyList(page.getResult(), list, SysMenuTplBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    @Override
    public Rsp getTemplate(SysMenuTplQueryReqBO reqBO) {
        log.info("获取菜单模板:{}", reqBO);
        SysMenuTpl record = sysMenuTplMapper.selectByPrimaryKey(reqBO.getTplCode());
        if (record != null) {
            SysMenuTplBO bo = new SysMenuTplBO();
            BeanUtils.copyProperties(record, bo);
            List<SysMenuBO> menuBOS = sysMenuBusiService.getMenusByTplCode(record.getTplCode());
            bo.setMenus(menuBOS);
            return BaseRspUtils.createSuccessRsp(bo);
        }
        return BaseRspUtils.createErrorRsp("模板不存在");
    }

    @Override
    public Rsp saveTemplate(SysMenuTplSaveReqBO reqBO) {
        if (checkMenuRel(reqBO)) {
            return BaseRspUtils.createErrorRsp("菜单关联信息不完整");
        }
        if (StringUtils.isNotBlank(reqBO.getTplCode())) {
            log.info("更新菜单模板:{}", reqBO);
            //更新
            SysMenuTpl record = sysMenuTplMapper.selectByPrimaryKey(reqBO.getTplCode());
            if (record == null) {
                return BaseRspUtils.createErrorRsp("模板不存在");
            }
            SysMenuTpl update = new SysMenuTpl();
            BeanUtils.copyProperties(reqBO, update);
            update.setUpdateTime(new Date());
            sysMenuTplMapper.updateByPrimaryKeySelective(update);
            updateMenuRel(reqBO.getTplCode(), reqBO);
            return BaseRspUtils.createSuccessRsp(record.getTplCode());
        } else {
            log.info("新增菜单模板:{}", reqBO);
            //新增
            SysMenuTpl record = new SysMenuTpl();
            BeanUtils.copyProperties(reqBO, record);
            record.setTplCode(IdWorker.nextAutoIdStr());
            record.setTplType(reqBO.getTplType());
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            sysMenuTplMapper.insertSelective(record);
            updateMenuRel(record.getTplCode(), reqBO);
            return BaseRspUtils.createSuccessRsp(record.getTplCode());
        }
    }

    private boolean checkMenuRel(SysMenuTplSaveReqBO reqBO) {
        if (reqBO.getMenuRels() != null && !reqBO.getMenuRels().isEmpty()) {
            for (SysMenuTplRelBO rel : reqBO.getMenuRels()) {
                if (StringUtils.isAnyBlank(rel.getMenuCode(), rel.getTplCode())) {
                    return false;
                }
            }
        }
        return true;
    }

    private void updateMenuRel(String tplCode, SysMenuTplSaveReqBO reqBO) {
        if (reqBO.getMenuRels() != null && !reqBO.getMenuRels().isEmpty()) {
            //删除重新写入
            sysMenuTplRelMenuMapper.deleteByTplCode(reqBO.getTplCode());
            List<SysMenuTplRelMenu> list = new ArrayList<>();
            for (SysMenuTplRelBO rel : reqBO.getMenuRels()) {
                rel.setTplCode(tplCode);
            }
            NiccCommonUtil.copyList(reqBO.getMenuRels(), list, SysMenuTplRelMenu.class);
            sysMenuTplRelMenuMapper.insertBatch(list);
        }
    }

    @Override
    public Rsp deleteTemplate(String tplCode) {
        if (StringUtils.isBlank(tplCode)) {
            return BaseRspUtils.createErrorRsp("模板编码不能为空");
        }
        log.info("删除菜单模板:{}", tplCode);
        SysMenuTpl update = new SysMenuTpl();
        update.setUpdateTime(new Date());
        update.setTplCode(tplCode);
        update.setIsValid(EntityValidType.DELETE.getCode());
        int i = sysMenuTplMapper.updateByPrimaryKeySelective(update);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(tplCode, "删除成功");
        }
        return BaseRspUtils.createErrorRsp("删除失败");
    }
}
