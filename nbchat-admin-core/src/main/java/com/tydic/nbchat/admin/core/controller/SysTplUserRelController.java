package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.SysTplUserRelApi;
import com.tydic.nbchat.admin.api.bo.SysTplUserRelReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/sys/templates/user")
public class SysTplUserRelController {
    private final SysTplUserRelApi sysTplUserRelApi;

    @PostMapping("/add")
    public Rsp add(@RequestBody SysTplUserRelReqBO reqBO) {
        return sysTplUserRelApi.createUserTplRel(reqBO);
    }

    @PostMapping("/list")
    public RspList queryList(@RequestBody SysTplUserRelReqBO reqBO){
        return sysTplUserRelApi.getUserRelList(reqBO);
    }

    @PostMapping("/delete")
    public Rsp delete(@RequestBody SysTplUserRelReqBO reqBO) {
        return sysTplUserRelApi.deleteUserTplRel(reqBO);
    }

    @PostMapping("/info")
    public Rsp queryInfo(@RequestBody SysTplUserRelReqBO reqBO) {
        return sysTplUserRelApi.getUserTplRelInfo(reqBO);
    }

    @PostMapping("/update")
    public Rsp queryById(@RequestBody SysTplUserRelReqBO reqBO) {
        return sysTplUserRelApi.updateUserTplRel(reqBO);
    }

    @PostMapping("/getUserMenu")
    public Rsp getUserMenu(@RequestBody SysTplUserRelReqBO reqBO) {
        return sysTplUserRelApi.getUserMenu(reqBO);
    }
}
