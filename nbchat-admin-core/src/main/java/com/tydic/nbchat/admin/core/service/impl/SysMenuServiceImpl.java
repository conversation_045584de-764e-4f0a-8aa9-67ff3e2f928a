package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.bo.menu.*;
import com.tydic.nbchat.admin.api.menu.SysMenuApi;
import com.tydic.nbchat.admin.core.busi.SysMenuBusiService;
import com.tydic.nbchat.admin.mapper.SysMenuButtonMapper;
import com.tydic.nbchat.admin.mapper.SysMenuMapper;
import com.tydic.nbchat.admin.mapper.po.SysMenu;
import com.tydic.nbchat.admin.mapper.po.SysMenuButton;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SysMenuServiceImpl implements SysMenuApi {

    @Resource
    private SysMenuMapper sysMenuMapper;
    @Resource
    private SysMenuButtonMapper sysMenuButtonMapper;

    private final SysMenuBusiService sysMenuBusiService;

    public SysMenuServiceImpl(SysMenuBusiService sysMenuBusiService) {
        this.sysMenuBusiService = sysMenuBusiService;
    }

    @Override
    public RspList getMenus(SysMenuQueryReqBO queryReqBO) {
        String menuType = StringUtils.isNotEmpty(queryReqBO.getMenuType()) ? queryReqBO.getMenuType() : "0";
        List<SysMenuBO> list = sysMenuBusiService.getMenus(queryReqBO.getMenuCode(),menuType);
        return BaseRspUtils.createSuccessRspList(list);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp saveMenu(SysMenuSaveReqBO reqBO) {
        if (reqBO.getId() != null) {
            SysMenu menu = sysMenuMapper.selectByPrimaryKey(reqBO.getId());
            if (menu == null) {
                return BaseRspUtils.createErrorRsp("菜单不存在");
            }
            //更新
            SysMenu update = new SysMenu();
            BeanUtils.copyProperties(reqBO, update);
            update.setCreateTime(new Date());
            sysMenuMapper.updateByPrimaryKeySelective(update);
            return BaseRspUtils.createSuccessRsp(getMenuInfo(reqBO.getId()), "更新成功");
        } else {
            if (StringUtils.isAnyBlank(reqBO.getMenuCode(), reqBO.getMenuName())) {
                return BaseRspUtils.createErrorRsp("参数异常");
            }
            if (sysMenuMapper.existMenuCode(reqBO.getMenuCode()) > 0) {
                return BaseRspUtils.createErrorRsp("菜单编码已存在");
            }
            SysMenu menu = new SysMenu();
            BeanUtils.copyProperties(reqBO, menu);
            menu.setCreateTime(new Date());
            sysMenuMapper.insertSelective(menu);
            return BaseRspUtils.createSuccessRsp(getMenuInfo(menu.getId()), "新增成功");
        }
    }


    private SysMenuBO getMenuInfo(Integer id) {
        SysMenuBO menuBO = new SysMenuBO();
        SysMenu menu = sysMenuMapper.selectByPrimaryKey(id);
        BeanUtils.copyProperties(menu, menuBO);
        return menuBO;
    }

    @Override
    public Rsp deleteMenu(String menuCode) {
        int i = sysMenuMapper.deleteByMenuCode(menuCode);
        return BaseRspUtils.createSuccessRsp(i);
    }

    @Override
    public Rsp saveButton(SysButtonSaveReqBO reqBO) {
        if (reqBO.getId() != null) {
            //更新
            SysMenuButton button = sysMenuButtonMapper.selectByPrimaryKey(reqBO.getId());
            if (button == null) {
                return BaseRspUtils.createErrorRsp("按钮不存在");
            }
            SysMenuButton update = new SysMenuButton();
            BeanUtils.copyProperties(reqBO, update);
            update.setUpdateTime(new Date());
            sysMenuButtonMapper.updateByPrimaryKeySelective(update);
            return BaseRspUtils.createSuccessRsp(getButtonInfo(reqBO.getId()), "更新成功");
        } else {
            if (StringUtils.isAnyBlank(reqBO.getMenuCode(), reqBO.getButtonCode(), reqBO.getButtonName())) {
                return BaseRspUtils.createErrorRsp("参数异常");
            }
            if (sysMenuButtonMapper.existButtonCode(reqBO.getButtonCode()) > 0) {
                return BaseRspUtils.createErrorRsp("按钮编码已存在");
            }
            SysMenuButton button = new SysMenuButton();
            BeanUtils.copyProperties(reqBO, button);
            button.setCreateTime(new Date());
            sysMenuButtonMapper.insertSelective(button);
            return BaseRspUtils.createSuccessRsp(getButtonInfo(button.getId()), "新增成功");
        }
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp deleteButton(SysButtonDeleteReqBO reqBO) {
        int i = sysMenuButtonMapper.deleteByCode(reqBO.getMenuCode(), reqBO.getButtonCode());
        return BaseRspUtils.createSuccessRsp(i);
    }

    private SysMenuButtonBO getButtonInfo(Integer id) {
        SysMenuButtonBO buttonBO = new SysMenuButtonBO();
        SysMenuButton button = sysMenuButtonMapper.selectByPrimaryKey(id);
        BeanUtils.copyProperties(button, buttonBO);
        return buttonBO;
    }
}
