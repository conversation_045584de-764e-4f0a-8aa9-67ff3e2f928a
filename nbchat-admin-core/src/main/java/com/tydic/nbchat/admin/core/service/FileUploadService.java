package com.tydic.nbchat.admin.core.service;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.RspList;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 15:51
 * @description 生成文件并上传
 */
public interface FileUploadService {

    /**
     * 生成Excel文件并上传
     *
     * @param baseInfo       上传用户基本数据
     * @param data           数据列表
     * @param fileExportName 文件名
     * @param clazz          数据格式
     * @param <T>            数据格式
     * @return 文件路径
     */
    <T> RspList<FileManageSaveBO> excelUpload(BaseInfo baseInfo, List<T> data, String fileExportName, Class<T> clazz);

    <T> RspList<FileManageSaveBO> excelUpload(BaseInfo baseInfo, List<T> data, String fileExportName, List<List<String>> head);
}
