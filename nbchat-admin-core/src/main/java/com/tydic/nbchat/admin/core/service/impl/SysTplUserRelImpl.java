package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.SysTplUserRelApi;
import com.tydic.nbchat.admin.api.bo.SysTplUserRelReqBO;
import com.tydic.nbchat.admin.api.bo.SysTplUserRelRspBO;
import com.tydic.nbchat.admin.api.bo.menu.SysMenuBO;
import com.tydic.nbchat.admin.core.busi.SysMenuBusiService;
import com.tydic.nbchat.admin.mapper.NameMapper;
import com.tydic.nbchat.admin.mapper.SysTplUserRelMapper;
import com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
public class SysTplUserRelImpl implements SysTplUserRelApi {

    private final SysMenuBusiService sysMenuBusiService;
    @Resource
    private SysTplUserRelMapper sysTplUserRelMapper;
    @Resource
    private NameMapper nameMapper;

    public SysTplUserRelImpl(SysMenuBusiService sysMenuBusiService) {
        this.sysMenuBusiService = sysMenuBusiService;
    }


    /**
     * 创建用户关联模板
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp createUserTplRel(SysTplUserRelReqBO reqBO) {
        log.info("创建用户关联模板-入参：{}", reqBO);
        if (StringUtils.isBlank(reqBO.getPhone())) {
            return BaseRspUtils.createErrorRsp("手机号不能为空");
        }
        if (StringUtils.isBlank(reqBO.getUserRealityName())) {
            return BaseRspUtils.createErrorRsp("真实姓名不能为空");
        }
        if (StringUtils.isBlank(reqBO.getTplCode())) {
            return BaseRspUtils.createErrorRsp("模板不能为空");
        }
        String userId = nameMapper.queryUserId(reqBO.getPhone());
        if (StringUtils.isBlank(userId)) {
            return BaseRspUtils.createErrorRsp("指定手机号找不到用户数据");
        }
        //判断是否存在
        int sum = sysTplUserRelMapper.selectInfoByUserIdCount(userId);
        if (sum > 0) {
            return BaseRspUtils.createErrorRsp("该用户存在绑定关系");
        }
        SysTplUserRelBO sysTplUserRelBO = new SysTplUserRelBO();
        BeanUtils.copyProperties(reqBO, sysTplUserRelBO);
        sysTplUserRelBO.setCreateBy(reqBO.get_userId());
        sysTplUserRelBO.setCreateTime(new Date());
        sysTplUserRelBO.setUpdateTime(new Date());
        sysTplUserRelBO.setUpdateBy(reqBO.get_userId());
        sysTplUserRelBO.setUserId(userId);
        sysTplUserRelBO.setTplCode(reqBO.getTplCode());
        sysTplUserRelMapper.updateRealName(sysTplUserRelBO);
        int count = sysTplUserRelMapper.insertSelective(sysTplUserRelBO);
        if (count == 0) {
            return BaseRspUtils.createErrorRsp("创建用户关联模板失败");
        }
        return BaseRspUtils.createSuccessRsp(sysTplUserRelBO, "创建用户关联模板成功");
    }

    @Override
    public RspList getUserRelList(SysTplUserRelReqBO reqBO) {
        log.info("获取用户关联模板列表-入参：{}", reqBO);
        SysTplUserRelBO condition = new SysTplUserRelBO();
        BeanUtils.copyProperties(reqBO, condition);
        Page<SysTplUserRelBO> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<SysTplUserRelRspBO> result = Lists.newArrayList();
        sysTplUserRelMapper.list(condition);
        NiccCommonUtil.copyList(page.getResult(), result, SysTplUserRelRspBO.class);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp updateUserTplRel(SysTplUserRelReqBO reqBO) {
        log.info("更新用户关联模板-入参：{}", reqBO);
        SysTplUserRelBO sysTplUserRelBO = new SysTplUserRelBO();
        BeanUtils.copyProperties(reqBO, sysTplUserRelBO);
        sysTplUserRelBO.setUpdateBy(reqBO.get_userId());
        sysTplUserRelBO.setUpdateTime(new Date());
        int count = sysTplUserRelMapper.updateByIdSelective(sysTplUserRelBO);
        if (count == 0) {
            return BaseRspUtils.createErrorRsp("更新用户关联模板失败");
        }
        return BaseRspUtils.createSuccessRsp("更新用户关联模板成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp deleteUserTplRel(SysTplUserRelReqBO reqBO) {
        log.info("删除用户关联模板-入参：{}", reqBO);
        int count = sysTplUserRelMapper.deleteById(reqBO.getId());
        if (count == 0) {
            return BaseRspUtils.createErrorRsp("删除用户关联模板失败");
        }
        return BaseRspUtils.createSuccessRsp(null, "删除用户关联模板成功");
    }

    @Override
    public Rsp getUserTplRelInfo(SysTplUserRelReqBO reqBO) {
        log.info("获取用户关联模板信息-入参：{}", reqBO);
        SysTplUserRelBO sysTplUserRelBO = sysTplUserRelMapper.selectInfoById(reqBO.getId());
        if (sysTplUserRelBO == null) {
            return BaseRspUtils.createErrorRsp("用户关联模板信息不存在");
        }
        return BaseRspUtils.createSuccessRsp(sysTplUserRelBO, "获取用户关联模板信息成功");
    }

    @Override
    public Rsp getUserMenu(SysTplUserRelReqBO reqBO) {
        String userId = reqBO.get_userId();
        if (StringUtils.isBlank(userId)) {
            return BaseRspUtils.createErrorRsp("请先登录");
        }
        SysTplUserRelBO sysTplUserRelBO = sysTplUserRelMapper.selectInfoByUserId(userId);
        List<SysMenuBO> menuList = new ArrayList<>();
        if(!Objects.isNull(sysTplUserRelBO)){
            menuList = sysMenuBusiService.getMenusByTplCode(sysTplUserRelBO.getTplCode());
        }
        return BaseRspUtils.createSuccessRsp(menuList);
    }


}
