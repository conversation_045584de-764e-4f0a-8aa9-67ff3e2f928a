# 推广搜索日志查询 API 文档

## 概述

本文档描述了推广搜索日志查询相关的 API 接口，支持百度、360、Bing 三个推广平台的搜索日志数据查询。

## 调用逻辑梳理

### 1. REST API 接口
- **URL**: `/train/promotion/searchLog`
- **方法**: GET
- **控制器**: `PromotionOAuthController.findRpSearchLog()`

### 2. 服务选择机制
根据 `appType` 参数自动选择对应的服务：
- `baidu` → `BaiduAppService`
- `360` → `E360AppService`  
- `bing` → `BingAppService`

### 3. Bing 服务操作详情

#### 使用的 Bing Ads API 服务：
1. **OAuth 2.0 认证**
   - URL: `https://login.microsoftonline.com/common/oauth2/v2.0/token`
   - 用途: 获取和刷新访问令牌

2. **报告服务**
   - URL: `https://reporting.api.bingads.microsoft.com/Api/Advertiser/Reporting/v13/ReportingService.svc`
   - 操作:
     - `SubmitGenerateReport`: 提交报告生成请求
     - `PollGenerateReport`: 轮询报告状态

3. **报告下载**
   - 通过返回的 URL 下载 CSV 格式报告

#### 是否可以使用 JSON 调用？
**不可以**。Bing Ads API 主要使用 **SOAP** 协议，不支持 JSON 格式的 REST API 调用。这是 Microsoft Bing Ads API 的设计特点。

## API 接口详情

### 1. 查找搜索日志记录

**接口地址**: `GET /train/promotion/searchLog`

**请求参数**:
- `appType` (必填): 推广平台类型，可选值: `baidu`、`360`、`bing`
- `startDate` (必填): 开始日期，格式: `yyyy-MM-dd`
- `endDate` (必填): 结束日期，格式: `yyyy-MM-dd`
- `promotionType` (可选): 推广类型，可选值: `computer`、`mobile`
- `page` (可选): 页码，默认为 1

**请求示例**:
```
GET /train/promotion/searchLog?appType=bing&startDate=2025-06-29&endDate=2025-06-30&promotionType=computer&page=1
```

**响应格式**:
```json
{
  "success": true,
  "message": "操作成功",
  "count": 10,
  "rows": [
    {
      "id": null,
      "dayData": "2025-06-30",
      "sources": "BingPC端",
      "term": "课件制作",
      "identifyClassify": null,
      "planScheme": "教育推广计划",
      "keywordGroups": "课件制作组",
      "keyword": "在线课件制作",
      "impressions": 1000,
      "click": 50,
      "consumption": 2500,
      "clickRate": 500,
      "avgClickPrice": 50,
      "matchMode": "Exact",
      "createTime": null
    }
  ]
}
```

### 2. Bing OAuth 相关接口

#### 2.1 生成授权URL
**接口地址**: `GET /train/oauth/bing/authUrl`

**请求参数**:
- `customerId` (必填): Bing Ads客户ID

#### 2.2 检查授权状态
**接口地址**: `GET /train/oauth/bing/status`

**请求参数**:
- `customerId` (必填): Bing Ads客户ID

#### 2.3 刷新访问令牌
**接口地址**: `POST /train/oauth/bing/refresh`

**请求参数**:
- `customerId` (必填): Bing Ads客户ID

#### 2.4 测试API连接性
**接口地址**: `GET /train/oauth/bing/test`

**请求参数**:
- `customerId` (必填): Bing Ads客户ID

## 错误处理

### 常见错误及解决方案

1. **404 Not Found 错误**
   - 原因: API URL 错误或服务不可用
   - 解决: 检查 API URL 配置，确认服务状态

2. **访问令牌获取失败**
   - 原因: OAuth 配置错误或令牌过期
   - 解决: 重新进行 OAuth 授权流程

3. **SOAP 请求格式错误**
   - 原因: SOAP 请求体格式不正确
   - 解决: 检查 SOAP 请求构建逻辑

4. **配置缺失**
   - 原因: 应用配置信息不完整
   - 解决: 检查配置文件中的相关配置项

## 配置说明

### Bing 推广配置示例
```yaml
nbchat-train:
  promotion:
    bing:
      limit: 300
      reportDataCustomerId: "254291993"
      redirectUri: "http://localhost:8705/train/callback/bing"
      appConfig:
        - customerId: "254291993"
          developerToken: "1209Q0Z961153714"
          clientId: "d5c38fb7-edd7-4793-a5fc-de9711aaf673"
          clientSecret: "****************************************"
          refreshToken: ""
          appName: "课件帮"
```

## 日志说明

系统会输出详细的日志信息，包括：
- 配置检查日志
- API 请求和响应日志
- 错误详情日志
- 数据解析日志

通过查看日志可以快速定位问题所在。

## 注意事项

1. Bing Ads API 使用 SOAP 协议，不支持 JSON 格式
2. 需要先完成 OAuth 授权才能调用 API
3. 报告生成是异步过程，需要轮询状态
4. 建议在测试环境先验证配置和连接性
5. 注意 API 调用频率限制
