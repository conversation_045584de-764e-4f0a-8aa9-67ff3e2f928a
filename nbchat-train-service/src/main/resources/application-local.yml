jasypt:
  encryptor:
    password: Ug8gYp75aV
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8  # 时区
    serialization: # 不返回时间戳
      write-dates-as-timestamps: false
  redis:
    host: ************** #************** #127.0.0.1
    port: 6389 #16379
    password: Redis235 #Redis235 #Mtj1qJSwLF
    timeout: 1000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-wait: -1
        max-active: 1000
        max-idle: 10
        min-idle: 5
    database: 5
  datasource:
    druid:
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      primary: master
      druid:
        initial-size: 3
        max-active: 10
        min-idle: 2
        max-wait: -1
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 30000
        time-between-eviction-runs-millis: 30000
        validation-query: select 1
        validation-query-timeout: -1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        pool-prepared-statements: true
        max-open-prepared-statements: 100
        filters: stat,wall
        share-prepared-statements: true
        wall:
          multi-statement-allow: true
      datasource:
        master:
          username: dml_user
          password: QfNWG4di_dml_dev
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************

# mybatis
mybatis:
  mapperLocations: classpath*:mapper/train/*.xml
  type-aliases-package: com.tydic.nbchat.*.mapper.po
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 分页组件
pagehelper:
  helperDialect: mysql
  params: count=countSql

nacos:
  config:
    server-addr: **************:8848 #**************:8848

# dubbo 服务配置
dubbo:
  application:
    name: nbchat-train
  registry:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    # 可以通过url ?namespace=nicc-env-dev&group=NICC的形式配置，也可以通过这种parameters配置
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  provider:
    threads: 300
    threadpool: cached
    loadbalance: roundrobin
    version: 1.0
    group: NICC
  protocol:
    name: dubbo
    port: -1
  # 配置中心和元数据
  config-center:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  metadata-report:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  consumer:
    check: false
    version: 1.0
    group: NICC

nicc-dc-config:
  dubbo-provider:
    version: 1.0
    group: NICC

# 数据中心相关组件配置
nicc-plugin:
  redis:
    enable: true # 是否开启redisHelper，开启后必须配置spring.redis，同时直接用RedisHelper
  oss:
    enable: false
  rest:
    enable: true # 开启restTemplate 组件
    connect-timeout: 5000
    read-timeout: 5000
    print-log: true
  kkmq:
    mq-type: rocketmq
    log-level: warn
    name-server: **************:9878 # rocketmq服务地址
    producer:
      group: nbchat_train # 自定义的组名称
    consumer:
      group-prefix: local
  minio:
    enable: true
    endpoint: http://**************:9090
    port: 9090
    accessKey: 8P1oTV9MvSTbxJwi
    secretKey: 1HcbEcNTzr7EjPEKsZps4gF8ChyfiHIC
    bucketName: nbchat-dev
    access-url: https://chatfiles-test.tydiczt.com/files

nbchat-train:
  config:
    course-generate-robot: chatgpt # 课程生成机器人
    page-join-enable: true #开启拼接
    page-join-word-limit: 500 #按页拼接字符上限
    anchor-config: ali
    scene-audio-enable: false
    modelscope-config:
      private-asr-api: http://172.168.1.249:8098/v1/tts/asr
      private-tts-api: http://172.168.1.249:8098/v1/tts_gateway/generate
    volcengine-config:
      app-id: 9060634121
      access-key: QP77_RDd-9CQwmdLZC6XBt_ImYMkH841
      secret-key: kfIgM17Sz9sqqQ9rt6Ti-VhWOrFoJsIb
      nls-base-url: https://openspeech.bytedance.com
    ali-config:
      access-key: LTAI5tJ542cAJW4YdxMabuix
      secret-key: ******************************
      tts:
        app-key: BsvzByW2YkGQxnZr
        tts-api: https://nls-gateway.cn-shanghai.aliyuncs.com/rest/v1/tts/async
    rp-config: # 报表配置
      timer-enable: true
    tdh-api: http://localhost:8080/tdh/task/submit
    degree-task-url-prefix: https://chat-test.tydiczt.com/chat/#/certificateExamDetails?taskId=
    degree-timer-enable: false
    promotion: # 推广平台配置
      sync-enable: true # 是否启用推广数据同步
      sync-cron: "0 0 3 * * ?" # 推广数据同步定时任务cron表达式，默认每天凌晨3点
      bing:
        limit: 300
        reportDataCustomerId: 254291993 # ✅ Bing Ads客户ID
        redirectUri: http://localhost:8705/train/callback/bing # ✅ OAuth回调地址
        appConfig:
          - customerId: 254291993 # ✅ 客户ID (同上)
            developerToken: 1209Q0Z961153714 # ✅ 开发者Token
            clientId: d5c38fb7-edd7-4793-a5fc-de9711aaf673 # ✅ 应用客户端ID
            clientSecret: **************************************** # ✅ 应用客户端密钥
            refreshToken: # ✅ 刷新Token
            appName: 课件帮 # ✅ 应用名称(自定义)


logging:
  config: classpath:logback-spring.xml
  level:
    com.ulisesbocchio.jasyptspringboot: debug
    com.tydic.nbchat.train.mapper: debug
    #com.tydic.nbchat.robot.mapper: warn
