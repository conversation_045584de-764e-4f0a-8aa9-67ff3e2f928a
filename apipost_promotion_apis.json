{"info": {"name": "推广搜索日志查询 API", "description": "支持百度、360、Bing三个推广平台的搜索日志数据查询接口", "version": "1.0.0"}, "item": [{"name": "推广搜索日志查询", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/promotion/searchLog?appType=bing&startDate=2025-06-29&endDate=2025-06-30&promotionType=computer&page=1", "host": ["{{baseUrl}}"], "path": ["train", "promotion", "searchLog"], "query": [{"key": "appType", "value": "bing", "description": "推广平台类型，可选值: baidu、360、bing"}, {"key": "startDate", "value": "2025-06-29", "description": "开始日期，格式: yyyy-MM-dd"}, {"key": "endDate", "value": "2025-06-30", "description": "结束日期，格式: yyyy-MM-dd"}, {"key": "promotionType", "value": "computer", "description": "推广类型，可选值: computer、mobile"}, {"key": "page", "value": "1", "description": "页码，默认为1"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "description": "根据指定的推广平台和时间范围查询搜索日志记录"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"count\": 10,\n  \"rows\": [\n    {\n      \"id\": null,\n      \"dayData\": \"2025-06-30\",\n      \"sources\": \"BingPC端\",\n      \"term\": \"课件制作\",\n      \"identifyClassify\": null,\n      \"planScheme\": \"教育推广计划\",\n      \"keywordGroups\": \"课件制作组\",\n      \"keyword\": \"在线课件制作\",\n      \"impressions\": 1000,\n      \"click\": 50,\n      \"consumption\": 2500,\n      \"clickRate\": 500,\n      \"avgClickPrice\": 50,\n      \"matchMode\": \"Exact\",\n      \"createTime\": null\n    }\n  ]\n}"}}]}, {"name": "<PERSON> - 生成授权URL", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/oauth/bing/authUrl?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "authUrl"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "生成Bing OAuth授权URL"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"data\": \"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=xxx&response_type=code&redirect_uri=xxx&scope=xxx&state=xxx\"\n}"}}]}, {"name": "<PERSON> OAuth - 检查授权状态", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/oauth/bing/status?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "status"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "检查Bing OAuth授权状态"}, "response": [{"name": "已授权", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"data\": \"已授权\"\n}"}}, {"name": "未授权", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"data\": \"未授权\"\n}"}}]}, {"name": "<PERSON> O<PERSON>uth - 刷新访问令牌", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/train/oauth/bing/refresh?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "refresh"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "手动刷新Bing访问令牌"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"data\": \"令牌刷新成功\"\n}"}}]}, {"name": "Bing OAuth - 测试API连接性", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/oauth/bing/test?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "test"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "测试Bing API连接性和配置"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"data\": {\n    \"configCheck\": \"开始检查配置...\",\n    \"tokenCheck\": \"访问令牌获取成功，长度: 1234\",\n    \"apiTest\": \"API调用成功，返回数据条数: 10\"\n  }\n}"}}]}, {"name": "<PERSON> OAuth - 生成SOAP请求示例", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/oauth/bing/soap-example?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "soap-example"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "生成Bing SOAP请求示例（用于调试）"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\"success\": true, \"message\": \"操作成功\", \"data\": \"<?xml version=\\\"1.0\\\" encoding=\\\"utf-8\\\"?>\\n<s:Envelope xmlns:s=\\\"http://schemas.xmlsoap.org/soap/envelope/\\\">\\n  <s:Header xmlns:h=\\\"https://bingads.microsoft.com/Reporting/v13\\\">\\n    <h:DeveloperToken>YOUR_DEV_TOKEN</h:DeveloperToken>\\n    <h:AuthenticationToken>YOUR_ACCESS_TOKEN</h:AuthenticationToken>\\n    <h:CustomerId>*********</h:CustomerId>\\n    <h:CustomerAccountId>*********</h:CustomerAccountId>\\n  </s:Header>\\n  <s:Body>\\n    ...\\n  </s:Body>\\n</s:Envelope>\"}"}}]}, {"name": "Bing OAuth - 测试SDK调用", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/train/oauth/bing/test-sdk?customerId=*********", "host": ["{{baseUrl}}"], "path": ["train", "o<PERSON>h", "bing", "test-sdk"], "query": [{"key": "customerId", "value": "*********", "description": "Bing Ads客户ID"}]}, "description": "测试Bing Ads SDK调用方式"}, "response": [{"name": "成功响应", "status": "OK", "code": 200, "body": {"mode": "raw", "raw": "{\"success\": true, \"message\": \"操作成功\", \"data\": {\"configCheck\": \"应用配置检查通过\", \"tokenCheck\": \"访问令牌获取成功，长度: 1234\", \"sdkTest\": \"SDK调用成功，返回记录数: 10\"}}"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8705", "description": "API基础URL"}]}