package com.tydic.nicc.dc.boot.starter.fastdfs;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Classname FastdfsAutoConfig
 * @Description FastdfsAutoConfig
 * @Date 2022/11/8 17:02
 * @Created by kangkang
 */
@Slf4j
@ConditionalOnProperty(value = "nicc-plugin.fdfs.enable",havingValue = "true")
@Configuration
public class FastdfsAutoConfig {

    @Autowired(required = false)
    private FastdfsConfigProperties fastdfsConfigProperties;

    @Bean
    public FastdfsHelper fastdfsHelper() {
        log.info("初始化 FastdfsHelper 配置...开始");
        if (this.fastdfsConfigProperties != null && this.fastdfsConfigProperties.check()) {
            log.info("初始化 FastdfsHelper 配置...完成:{}", JSONObject.toJSONString(fastdfsConfigProperties));
            return new FastdfsHelper(fastdfsConfigProperties);
        } else {
            throw new IllegalArgumentException("FastdfsHelper 文件上传属性配置错误，请检查配置文件");
        }
    }

}
