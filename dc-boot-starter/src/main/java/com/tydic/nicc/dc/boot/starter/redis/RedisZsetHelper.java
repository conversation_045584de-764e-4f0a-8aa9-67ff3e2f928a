package com.tydic.nicc.dc.boot.starter.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@ConditionalOnProperty(value = "nicc-plugin.redis.enable",havingValue = "true")
@Component
public class RedisZsetHelper {

    @Autowired(required = false)
    private StringRedisTemplate redisTemplate;

    public boolean zSetAdd(String key,String val,Double score){
        return redisTemplate.opsForZSet().add(key,val,score);
    }

    public void zSetRemove(String key,String ...val){
        redisTemplate.opsForZSet().remove(key,val);
    }

    public Long zSetSize(String key){
        return redisTemplate.opsForZSet().size(key);
    }

    public Long zSetRank(String key,String val){
        return redisTemplate.opsForZSet().rank(key,val);
    }

    public Long zSetReverseRank(String key,String val){
        return redisTemplate.opsForZSet().reverseRank(key,val);
    }

    public Double zSetScore(String key,String val){
        return redisTemplate.opsForZSet().score(key,val);
    }

    public void zSetIncrementScore(String key,String val,Double score){
        redisTemplate.opsForZSet().incrementScore(key,val,score);
    }

    public void zSetRemoveRange(String key,Long start,Long end){
        redisTemplate.opsForZSet().removeRange(key,start,end);
    }

    public void zSetRemoveRangeByScore(String key,Double min,Double max){
        redisTemplate.opsForZSet().removeRangeByScore(key,min,max);
    }

    public Set<String> zSetRangeByScore(String key, Double min, Double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    public Set<String> zSetRange(String key, Long start, Long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

}