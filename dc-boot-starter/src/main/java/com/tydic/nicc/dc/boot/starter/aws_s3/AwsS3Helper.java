package com.tydic.nicc.dc.boot.starter.aws_s3;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.tydic.nicc.dc.boot.starter.api.AbstractFileHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.*;

@Slf4j
public class AwsS3Helper extends AbstractFileHelper {

    private AwsS3ConfigProperties awsS3ConfigProperties;
    private AmazonS3 amazonS3;
    private String tmpPath = System.getProperty("java.io.tmpdir");


    public AwsS3Helper(AwsS3ConfigProperties properties) {
        this.awsS3ConfigProperties = properties;
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setMaxConnections(properties.getMaxConnections());
        if (StringUtils.isNotBlank(properties.getProtocol()) && properties.getProtocol().equals("http")) {
            clientConfiguration.setProtocol(Protocol.HTTP);
        }
        AwsClientBuilder.EndpointConfiguration endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(
                properties.getEndpoint(), properties.getRegion());
        AWSCredentials awsCredentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getAccessKeySecret());
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        this.amazonS3 = AmazonS3Client.builder().withEndpointConfiguration(endpointConfiguration)
                .withClientConfiguration(clientConfiguration).withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding().withPathStyleAccessEnabled(properties.getPathStyleAccess()).build();
    }

    /**
     * 获取签名地址，用于前端直传
     *
     * @param bucketName
     * @param objectName
     * @param metadata
     * @return
     */
    public String getPrePutUrl(String bucketName, String objectName, Map<String, String> metadata) {
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName);
        request.setMethod(HttpMethod.PUT);
        request.setExpiration(new Date(System.currentTimeMillis() + 3600 * 1000));
        if (metadata != null) {
            metadata.forEach(request::addRequestParameter);
            request.setContentType(metadata.get("Content-Type"));
        }
        if (StringUtils.isBlank(request.getContentType())) {
            request.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        }
        URL url = amazonS3.generatePresignedUrl(request);
        return replacePrePutUrl(awsS3ConfigProperties.getAccessUrl(), url.toString(), bucketName);
    }

    public String getPrePutUrl(String objectName, Map<String, String> queryParams) {
        return getPrePutUrl(awsS3ConfigProperties.getBucketName(), objectName, queryParams);
    }


    /**
     * 获取下载链接
     * @param objectName
     * @param metadata
     * @return
     */
    public String getPreGetUrl(String objectName, Map<String, String> metadata) {
        String bucketName = this.awsS3ConfigProperties.getBucketName();
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(this.awsS3ConfigProperties.getBucketName(), objectName);
        request.setMethod(HttpMethod.GET);
        request.setExpiration(new Date(System.currentTimeMillis() + 3600 * 1000));
        if (metadata != null) {
            metadata.forEach(request::addRequestParameter);
        }
        URL url = amazonS3.generatePresignedUrl(request);
        return replacePrePutUrl(awsS3ConfigProperties.getAccessUrl(), url.toString(), bucketName);
    }

    /**
     * 获取策略
     *
     * @return
     */
    public String getPolicyText() {
        BucketPolicy bucket_policy = amazonS3.getBucketPolicy(awsS3ConfigProperties.getBucketName());
        return bucket_policy.getPolicyText();
    }


    public void createBucket(String bucketName) {
        // 检验bucket是否存在
        if (!amazonS3.doesBucketExistV2(bucketName)) {
            amazonS3.createBucket((bucketName));
        }
    }

    public List<Bucket> getAllBuckets() {
        return amazonS3.listBuckets();
    }


    /**
     * @param file 文件对象
     * @return
     */
    public PutObjectResult upload(File file) {
        return upload(file.getName(), file);
    }


    /**
     * 文件上传， 默认公共读
     *
     * @param objectName
     * @param file
     * @return
     */
    public PutObjectResult upload(String objectName, File file) {
        return upload(objectName, file, CannedAccessControlList.PublicRead);
    }

    /**
     * @param objectName 完整文件路径 /xx/xx/abc.png
     * @param file       文件对象
     * @return
     */
    public PutObjectResult upload(String objectName, File file, CannedAccessControlList cannedAccessControlList) {
        objectName = checkObjectName(objectName);
        PutObjectRequest putObjectRequest = new PutObjectRequest(awsS3ConfigProperties.getBucketName(),
                objectName, file);
        try {
            // 上传
            putObjectRequest.setCannedAcl(cannedAccessControlList);
            return amazonS3.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败:", e);
        }
    }

    /**
     * @param objectName  完整文件路径 xx/xx/abc.png
     * @param inputStream 文件流
     * @return
     */
    public PutObjectResult upload(String objectName, InputStream inputStream) {
        return upload(awsS3ConfigProperties.getBucketName(), "", objectName, inputStream);
    }

    /**
     * 文件上传
     *
     * @param bucketName
     * @param dirPath
     * @param fileName
     * @param inputStream
     * @return
     */
    public PutObjectResult upload(String bucketName, String dirPath, String fileName, InputStream inputStream) {
        return upload(bucketName, dirPath, fileName, inputStream, CannedAccessControlList.PublicRead);
    }

    /**
     * @param bucketName  桶名称
     * @param dirPath     文件路径 xx/xx
     * @param fileName    文件名称 abc.png
     * @param inputStream 文件流
     * @return
     */
    public PutObjectResult upload(String bucketName, String dirPath, String fileName,
                                  InputStream inputStream, CannedAccessControlList cannedAccessControlList) {
        String objectName = getObjectName(dirPath, fileName);
        objectName = checkObjectName(objectName);
        try {
            ObjectMetadata metadata = getMetadata(objectName);
            metadata.setContentLength(inputStream.available());
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream, metadata);
            putObjectRequest.setCannedAcl(cannedAccessControlList);
            log.info("上传文件-开始:{}|{}", awsS3ConfigProperties.getBucketName(), objectName);
            return amazonS3.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("上传文件-异常:", e);
        }
    }

    /**
     * 下载文件
     *
     * @param objectName 对象名称 /xx/xx/xx.jpg
     * @return S3Object
     */
    public S3Object download(String objectName) {
        return download(this.awsS3ConfigProperties.getBucketName(), objectName, null);
    }

    /**
     * 下载文件
     *
     * @param bucketName bucket
     * @param objectName 对象名称 /xx/xx/xx.jpg
     * @return
     * @throws Exception
     */
    public S3Object download(String bucketName, String objectName, String localFilePath) {
        objectName = checkObjectName(objectName);
        log.info("下载文件-开始:{}|{}|{}", bucketName, objectName, localFilePath);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, objectName);
        if (StringUtils.isNotBlank(localFilePath)) {
            // 如果指定了本地路径，则下载到本地
            File localFile = new File(localFilePath);
            if (!localFile.getParentFile().exists()) {
                localFile.getParentFile().mkdirs();
            }
            log.info("下载文件-完成:{}|{}|{}", bucketName, objectName, localFilePath);
            amazonS3.getObject(getObjectRequest, localFile);
            return null;
        } else {
            return amazonS3.getObject(getObjectRequest);
        }
    }

    /**
     * 下载文件
     *
     * @param objectName
     * @return
     */
    public InputStream downloadFile(String objectName) {
        return downLoadToInputStream(objectName);
    }

    /**
     * 下载文件到 InputStream
     * @param objectName
     * @return
     */
    public InputStream downLoadToInputStream(String objectName) {
        S3Object object = download(awsS3ConfigProperties.getBucketName(), objectName, null);
        return object.getObjectContent();
    }

    /**
     * 下载文件
     *
     * @param objectName
     * @param localPath
     * @throws Exception
     */
    public void downloadToFile(String objectName, String localPath) throws Exception {
        download(awsS3ConfigProperties.getBucketName(), objectName, localPath);
    }


    /**
     * 删除文件
     *
     * @param objectName
     * @throws Exception
     */
    public void delete(String objectName) {
        delete(awsS3ConfigProperties.getBucketName(), Collections.singletonList(objectName));
    }

    /**
     * 删除文件
     *
     * @param bucketName  桶名称
     * @param objectNames 批量删除
     * @throws Exception
     */
    public void delete(String bucketName, List<String> objectNames) {
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName);
        // 设置要删除的key列表, 最多一次删除1000个
        ArrayList<DeleteObjectsRequest.KeyVersion> keyList = new ArrayList<>();
        // 传入要删除的文件名
        // 注意文件名不允许以正斜线/或者反斜线\开头，例如：
        // 存储桶目录下有a/b/c.txt文件，如果要删除，
        // 只能是 keyList.add(new KeyVersion("a/b/c.txt")),
        // 若使用 keyList.add(new KeyVersion("/a/b/c.txt"))会导致删除不成功
        for (String objectName : objectNames) {
            objectName = checkObjectName(objectName);
            keyList.add(new DeleteObjectsRequest.KeyVersion(objectName));
        }
        log.info("删除文件-开始:{}|{}", bucketName, objectNames);
        deleteObjectsRequest.setKeys(keyList);
        try {
            DeleteObjectsResult deleteObjectsResult = amazonS3.deleteObjects(deleteObjectsRequest);
            List<DeleteObjectsResult.DeletedObject> resultDeletedObjects = deleteObjectsResult.getDeletedObjects();
            log.info("删除文件-完成:{}|{}", bucketName, JSONObject.toJSON(resultDeletedObjects));
        } catch (MultiObjectDeleteException mde) {
            // 如果部分删除成功部分失败, 返回 MultiObjectDeleteException
            List<DeleteObjectsResult.DeletedObject> deleteObjects = mde.getDeletedObjects();
            List<MultiObjectDeleteException.DeleteError> deleteErrors = mde.getErrors();
            log.warn("删除文件-部分失败:{}| delete_list = {}|error_list = {}", bucketName,
                    JSONObject.toJSON(deleteObjects), JSONObject.toJSON(deleteErrors));
        } catch (Exception e) {
            throw new RuntimeException("删除文件-异常:", e);
        }
    }

    public List<S3ObjectSummary> listFiles(String prefix) {
        return listFiles(awsS3ConfigProperties.getBucketName(), prefix);
    }


    /**
     * 列出文件
     *
     * @param bucketName
     * @return
     */
    public List<S3ObjectSummary> listFiles(String bucketName, String prefix) {
        // 通过被截断的列对象请求获取，参考上面的 列出第一页对象
        String nextMarker = "nextMarker";
        log.info("列出文件-开始:{}|{}", awsS3ConfigProperties.getBucketName(), prefix);
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        // 设置 bucket 名称
        listObjectsRequest.setBucketName(bucketName);
        // 设置列出的对象名以 prefix 为前缀
        listObjectsRequest.setPrefix(prefix);
        // 设置最大列出多少个对象, 一次 listobject 最大支持1000
        listObjectsRequest.setMaxKeys(1000);
        // 设置被截断开始的位置
        listObjectsRequest.setMarker(nextMarker);
        ObjectListing objectListing = null;
        List<S3ObjectSummary> cosObjectSummaries = new ArrayList<>();
        try {
            objectListing = amazonS3.listObjects(listObjectsRequest);
            // object summary 表示此次列出的对象列表
            cosObjectSummaries = objectListing.getObjectSummaries();
        } catch (Exception e) {
            log.info("列出文件-异常:{}|{}", awsS3ConfigProperties.getBucketName(), prefix, e);
            e.printStackTrace();
        }
        return cosObjectSummaries;
    }


    /**
     * 获取对象键
     *
     * @param dirPath
     * @param fileName
     * @return
     */
    public String getObjectName(String dirPath, String fileName) {
        String objectName = "";
        if (StringUtils.isBlank(dirPath)) {
            objectName = fileName;
        } else {
            if (dirPath.endsWith("/")) {
                objectName = dirPath + fileName;
            } else {
                objectName = dirPath + File.separator + fileName;
            }
        }
        return objectName;
    }


    /**
     * 获取 metadata
     *
     * @param fileName
     * @return
     */
    private ObjectMetadata getMetadata(String fileName) {
        String contentType = MediaTypeFactory.getMediaType(fileName).
                orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
        //若需要设置对象的自定义 Headers 可参照下列代码,若不需要可省略下面这几行,
        // 对象自定义 Headers 的详细信息可参考 https://cloud.tencent.com/document/product/436/13361
        ObjectMetadata objectMetadata = new ObjectMetadata();
        //若设置 Content-Type、Cache-Control、Content-Disposition、Content-Encoding、Expires
        // 这五个字自定义 Headers，推荐采用 objectMetadata.setHeader()
        objectMetadata.setHeader("Content-Type", contentType);
        //若要设置 “x-cos-meta-[自定义后缀]” 这样的自定义 Header，推荐采用
        //Map<String, String> userMeta = new HashMap<String, String>();
        //userMeta.put("x-cos-meta-[自定义后缀]", "value");
        //objectMetadata.setUserMetadata(userMeta);
        return objectMetadata;
    }


    private String checkObjectName(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            throw new RuntimeException("对象名称不得为空!");
        }
        return objectName.startsWith("/") ? objectName.substring(1) : objectName;
    }

}
