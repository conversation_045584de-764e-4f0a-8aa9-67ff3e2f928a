package com.tydic.nicc.common.bo.event.trigger;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: 用户状态变更触发  <br>
 * @date 2021/5/8 3:20 下午  <br>
 * @Copyright tydic.com
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserStatusChangeTrigger extends EventTriggerData {

    private String userId;
    //offline|online
    private String status;
    //状态变更时间
    private Date changeTime;

}
