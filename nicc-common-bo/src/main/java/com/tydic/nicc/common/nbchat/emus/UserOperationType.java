package com.tydic.nicc.common.nbchat.emus;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum UserOperationType {

    // 页面
    PAGE("page", "页面"),
    // 登录
    LOGIN("login", "登录"),
    // 登出
    LOGOUT("logout", "登出"),
    // 注册
    REGISTER("reg", "注册"),
    // ppt下载
    PPT_DOWNLOAD("ppt_down", "ppt下载"),
    // 视频下载
    VIDEO_DOWNLOAD("video_down", "视频下载"),
    // 试题生成
    EXAM_GENE("exam_gene", "试题生成"),
    // 试题导出
    EXAM_EXPORT("exam_export", "试题导出"),
    // 会员开通点击页面
    VIP_PAGE("vip_page", "会员开通点击页面"),
    // 视频分享
    VIDEO_SHARE("video_share", "视频分享"),
    // 分享视频 web 序浏览
    VIDEO_WEB_VIEW("video_web_view", "分享视频 web 序浏览"),
    // 会员开通点击页面
    VIDEO_MINI_VIEW("video_mini_view", "分享视频小程序浏览"),
    // 邀请链接浏览
    REBATE_VIEW("rebate_view", "邀请链接浏览");

    private final String type;
    private final String name;

    public static UserOperationType getEnumByType(String type) {
        return Arrays.stream(values()).filter(o -> o.type.equals(type)).findFirst().orElse(null);
    }

    public static String getNameByType(String type) {
        return Arrays.stream(values()).filter(o -> o.type.equals(type)).findFirst().map(o -> o.name).orElse("");
    }
}
