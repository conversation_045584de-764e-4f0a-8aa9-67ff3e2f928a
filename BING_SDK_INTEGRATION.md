# Bing Ads SDK 集成说明

## 概述

将原有的手动构建 SOAP 请求的方式改为使用 Microsoft Bing Ads SDK，提供更稳定、更易维护的 API 调用方式。

## 主要改动

### 1. 添加 SDK 依赖

在 `pom.xml` 中添加了以下依赖：

```xml
<!-- Bing Ads SDK -->
<dependency>
    <groupId>com.microsoft.bingads</groupId>
    <artifactId>microsoft.bingads</artifactId>
    <version>13.0.18</version>
</dependency>

<!-- JAX-WS API for SOAP -->
<dependency>
    <groupId>javax.xml.ws</groupId>
    <artifactId>jaxws-api</artifactId>
    <version>2.3.1</version>
</dependency>

<!-- JAX-WS Runtime -->
<dependency>
    <groupId>com.sun.xml.ws</groupId>
    <artifactId>jaxws-rt</artifactId>
    <version>2.3.5</version>
</dependency>
```

### 2. 新增文件

#### 2.1 BingSdkService.java
- 封装了 Bing Ads SDK 的调用逻辑
- 提供了 `findSearchLogWithSdk` 方法
- 包含完整的认证、报告生成、轮询、下载和解析流程

### 3. 修改的文件

#### 3.1 BingPromotionConfigProperties.java
- 添加了 `useSdk` 配置项，默认为 `true`
- 允许用户选择使用 SDK 还是 SOAP 方式

#### 3.2 BingAppService.java
- 集成了 `BingSdkService`
- 修改了 `findRpSearchLog` 方法，根据配置选择调用方式
- 添加了 `findRpSearchLogWithSdk` 和 `findRpSearchLogWithSoap` 方法
- 保留了原有的 SOAP 调用作为备用方案

#### 3.3 PromotionOAuthController.java
- 添加了 `/train/oauth/bing/test-sdk` 测试接口
- 用于测试 SDK 调用方式的功能

## SDK 调用流程

### 1. 认证设置
```java
AuthorizationData authorizationData = new AuthorizationData();
authorizationData.setAuthentication(new OAuthWebAuthCodeGrant(...));
authorizationData.setCustomerId(Long.parseLong(customerId));
authorizationData.setAccountId(Long.parseLong(customerAccountId));
authorizationData.setDeveloperToken(developerToken);
```

### 2. 创建服务客户端
```java
ServiceClient<IReportingService> reportingService = ReportingServiceManager.getInstance()
        .getReportingServiceClient(authorizationData, ApiEnvironment.PRODUCTION);
```

### 3. 构建报告请求
```java
SearchQueryPerformanceReportRequest reportRequest = new SearchQueryPerformanceReportRequest();
// 设置报告参数...
```

### 4. 提交和轮询
```java
SubmitGenerateReportResponse submitResponse = reportingService.getService()
        .submitGenerateReport(reportRequest);
        
// 轮询报告状态
PollGenerateReportResponse pollResponse = reportingService.getService()
        .pollGenerateReport(reportRequestId);
```

## 配置说明

### 完整配置示例
```yaml
nbchat-train:
  config:
    promotion:
      bing:
        limit: 300
        reportDataCustomerId: "*********"
        redirectUri: "http://localhost:8705/train/callback/bing"
        useSdk: true                                 # 使用SDK方式（推荐）
        appConfig:
          - customerId: "*********"                  # 经理账号ID
            developerToken: "1209Q0Z961153714"       # 开发者令牌
            clientId: "d5c38fb7-edd7-4793-a5fc-de9711aaf673"
            clientSecret: "****************************************"
            customerAccountId: "*********"           # 广告账户ID（必填）
            refreshToken: ""
            appName: "课件帮"
```

### 配置项说明
- **useSdk**: 
  - `true`: 使用 Bing Ads SDK（推荐）
  - `false`: 使用手动构建的 SOAP 请求

## SDK vs SOAP 对比

| 特性 | SDK 方式 | SOAP 方式 |
|------|----------|-----------|
| **稳定性** | 高，Microsoft官方维护 | 中，需要手动维护SOAP格式 |
| **易用性** | 高，面向对象API | 低，需要构建XML |
| **错误处理** | 完善的异常处理 | 需要手动解析SOAP错误 |
| **版本兼容** | 自动处理API版本变化 | 需要手动适配 |
| **性能** | 优化的网络调用 | 标准HTTP请求 |
| **维护成本** | 低 | 高 |

## 测试接口

### 1. SDK 调用测试
```
GET /train/oauth/bing/test-sdk?customerId=*********
```

### 2. 连接性测试
```
GET /train/oauth/bing/test?customerId=*********
```

### 3. SOAP 请求示例
```
GET /train/oauth/bing/soap-example?customerId=*********
```

## 优势

### 1. 更好的错误处理
- SDK 提供了结构化的异常处理
- 自动处理网络重试和超时
- 详细的错误信息和错误码

### 2. 自动化的认证管理
- 自动处理 OAuth 令牌刷新
- 内置的认证流程
- 安全的令牌存储

### 3. 类型安全
- 强类型的 API 接口
- 编译时错误检查
- IntelliSense 支持

### 4. 版本兼容性
- 自动适配 API 版本变化
- 向后兼容性保证
- 官方维护和更新

## 迁移建议

1. **新项目**: 直接使用 SDK 方式（`useSdk: true`）
2. **现有项目**: 
   - 先保持 SOAP 方式运行（`useSdk: false`）
   - 在测试环境验证 SDK 方式
   - 确认无问题后切换到 SDK 方式

## 注意事项

1. **依赖管理**: 确保 JAX-WS 相关依赖正确配置
2. **环境配置**: SDK 默认使用生产环境，测试时注意数据
3. **错误处理**: SDK 异常和 SOAP 错误的处理方式不同
4. **性能监控**: 监控 SDK 调用的性能和成功率

## 故障排除

### 常见问题
1. **ClassNotFoundException**: 检查 JAX-WS 依赖是否正确
2. **认证失败**: 确认 OAuth 令牌和配置正确
3. **权限错误**: 检查 customerAccountId 配置
4. **网络超时**: 调整超时配置或检查网络连接

### 调试建议
1. 使用测试接口验证配置
2. 查看详细的日志输出
3. 对比 SDK 和 SOAP 的调用结果
4. 检查 Bing Ads API 的状态页面
