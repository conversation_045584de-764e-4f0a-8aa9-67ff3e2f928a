package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:27
 * @description
 */
@Data
public class SysTplUserRelBO implements Serializable {


    private Integer id;

    /**
     * 模板名称
     */
    private String tplName;
    /**
     * 真实姓名
     */
    private String userRealityName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 模板编码
     */
    private String tplCode;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 排序用
     */
    private String sort;
}
