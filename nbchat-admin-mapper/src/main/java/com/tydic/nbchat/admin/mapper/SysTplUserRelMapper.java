package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:24
 * @description 用户模板关联关系Mapper
 */
public interface SysTplUserRelMapper {

    /**
     * 根据提供的参数列表检索用户模板关联关系信息。
     *
     * @param sysTplUserRelBO 包含查询条件的SysTplUserRelBO对象
     * @return 包含符合条件的用户画像模板关系信息的列表
     */
    List<SysTplUserRelBO> list(SysTplUserRelBO sysTplUserRelBO);

    /**
     * 根据用户ID列表检索用户模板关联关系信息。
     *
     * @param id id
     * @return 包含符合条件的用户模板关联关系信息的列表
     */
    SysTplUserRelBO selectInfoById(@Param("id") Integer id);

    /**
     * 根据用户ID列表检索用户模板关联关系信息count
     *
     * @param userId id
     * @return 包含符合条件的用户模板关联关系信息的列表
     */
    int selectInfoByUserIdCount(@Param("userId") String userId);

    /**
     * 根据用户ID列表检索用户模板关联关系信息。
     *
     * @param userId id
     * @return 包含符合条件的用户模板关联关系信息的列表
     */
    SysTplUserRelBO selectInfoByUserId(@Param("userId") String userId);
    /**
     * 插入用户模板关联关系信息。
     *
     * @param sysTplUserRelBO 包含要插入的用户模板关联关系信息的SysTplUserRelBO对象
     * @return 插入操作影响的行数
     */
    int insertSelective(SysTplUserRelBO sysTplUserRelBO);

    /**
     * 根据主键删除用户模板关联关系信息。
     *
     * @param id 要删除的用户模板关联关系的主键值
     * @return 删除操作影响的行数
     */
    int deleteById(@Param("id") Integer id);

    /**
     * 根据主键更新用户模板关联关系信息。
     *
     * @param sysTplUserRelBO 包含要更新的用户模板关联关系信息的SysTplUserRelBO对象
     * @return 更新操作影响的行数
     */
    int updateByIdSelective(SysTplUserRelBO sysTplUserRelBO);

    /**
     * 根据主键更新用户模板关联关系信息。
     *
     * @param sysTplUserRelBO 包含要更新的用户模板关联关系信息的SysTplUserRelBO对象
     * @return 更新操作影响的行数
     */
    int updateRealName(SysTplUserRelBO sysTplUserRelBO);
}
