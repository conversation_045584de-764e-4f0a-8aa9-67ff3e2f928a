<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysMenuMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysMenu" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="parent_code" property="parentCode" jdbcType="VARCHAR" />
    <result column="menu_code" property="menuCode" jdbcType="VARCHAR" />
    <result column="menu_name" property="menuName" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="SMALLINT" />
    <result column="route_path" property="routePath" jdbcType="VARCHAR" />
    <result column="is_new_window" property="isNewWindow" jdbcType="CHAR" />
    <result column="is_ext_system" property="isExtSystem" jdbcType="CHAR" />
    <result column="icon" property="icon" jdbcType="VARCHAR" />
    <result column="is_show" property="isShow" jdbcType="CHAR" />
    <result column="is_valid" property="isValid" jdbcType="CHAR" />
    <result column="order_index" property="orderIndex" jdbcType="SMALLINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="menu_type" property="menuType" jdbcType="CHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, parent_code, menu_code, menu_name, level, route_path, is_new_window, is_ext_system, 
    icon, is_show, is_valid, order_index, remark, create_time, update_time, menu_type
  </sql>

  <select id="selectByCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_menu where is_valid = '1'
    <if test="menuCode != null and menuCode != ''">
        and menu_code = #{menuCode}
    </if>
    <if test="menuType != null and menuType != ''">
      and menu_type = #{menuType}
    </if>
    order by order_index,id
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from sys_menu
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByMenuCode" parameterType="string">
    delete from sys_menu where menu_code = #{menuCode}
  </delete>

  <select id="existMenuCode" parameterType="string" resultType="int">
    select count(1) from sys_menu where menu_code = #{menuCode}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from sys_menu
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
          parameterType="com.tydic.nbchat.admin.mapper.po.SysMenu" >
    insert into sys_menu
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="parentCode != null" >
        parent_code,
      </if>
      <if test="menuCode != null" >
        menu_code,
      </if>
      <if test="menuName != null" >
        menu_name,
      </if>
      <if test="level != null" >
        level,
      </if>
      <if test="routePath != null" >
        route_path,
      </if>
      <if test="isNewWindow != null" >
        is_new_window,
      </if>
      <if test="isExtSystem != null" >
        is_ext_system,
      </if>
      <if test="icon != null" >
        icon,
      </if>
      <if test="isShow != null" >
        is_show,
      </if>
      <if test="isValid != null" >
        is_valid,
      </if>
      <if test="orderIndex != null" >
        order_index,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="menuType != null" >
        menu_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="parentCode != null" >
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="menuCode != null" >
        #{menuCode,jdbcType=VARCHAR},
      </if>
      <if test="menuName != null" >
        #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="level != null" >
        #{level,jdbcType=SMALLINT},
      </if>
      <if test="routePath != null" >
        #{routePath,jdbcType=VARCHAR},
      </if>
      <if test="isNewWindow != null" >
        #{isNewWindow,jdbcType=CHAR},
      </if>
      <if test="isExtSystem != null" >
        #{isExtSystem,jdbcType=CHAR},
      </if>
      <if test="icon != null" >
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null" >
        #{isShow,jdbcType=CHAR},
      </if>
      <if test="isValid != null" >
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="orderIndex != null" >
        #{orderIndex,jdbcType=SMALLINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="menuType != null" >
        #{menuType,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenu" >
    update sys_menu
    <set >
      <if test="parentCode != null" >
        parent_code = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="menuName != null" >
        menu_name = #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="level != null" >
        level = #{level,jdbcType=SMALLINT},
      </if>
      <if test="routePath != null" >
        route_path = #{routePath,jdbcType=VARCHAR},
      </if>
      <if test="isNewWindow != null" >
        is_new_window = #{isNewWindow,jdbcType=CHAR},
      </if>
      <if test="isExtSystem != null" >
        is_ext_system = #{isExtSystem,jdbcType=CHAR},
      </if>
      <if test="icon != null" >
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null" >
        is_show = #{isShow,jdbcType=CHAR},
      </if>
      <if test="isValid != null" >
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="orderIndex != null" >
        order_index = #{orderIndex,jdbcType=SMALLINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="menuType != null" >
        menu_type = #{menuType},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>