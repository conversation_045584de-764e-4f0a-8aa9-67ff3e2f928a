<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserPaymentInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_payment_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="is_subscriber" jdbcType="CHAR" property="isSubscriber"/>
        <result column="is_refund" jdbcType="CHAR" property="isRefund"/>
        <result column="total_refund_amount" jdbcType="INTEGER" property="totalRefundAmount"/>
        <result column="refund_num" jdbcType="INTEGER" property="refundNum"/>
        <result column="total_pay_amount" jdbcType="INTEGER" property="totalPayAmount"/>
        <result column="pay_num" jdbcType="INTEGER" property="payNum"/>
        <result column="first_pay_time" jdbcType="TIMESTAMP" property="firstPayTime"/>
        <result column="last_pay_time" jdbcType="TIMESTAMP" property="lastPayTime"/>
        <result column="first_vip_time" jdbcType="TIMESTAMP" property="firstVipTime"/>
        <result column="last_vip_time" jdbcType="TIMESTAMP" property="lastVipTime"/>
        <result column="total_vip_amount" jdbcType="INTEGER" property="totalVipAmount"/>
        <result column="score_recharge_total" jdbcType="INTEGER" property="scoreRechargeTotal"/>
        <result column="score_consume_total" jdbcType="INTEGER" property="scoreConsumeTotal"/>
        <result column="score_consume_total_person" jdbcType="INTEGER" property="scoreConsumeTotalPerson"/>
        <result column="score_lose_total" jdbcType="INTEGER" property="scoreLoseTotal"/>
        <result column="score_balance" jdbcType="INTEGER" property="scoreBalance"/>
        <result column="score_total_count" jdbcType="INTEGER" property="scoreTotalCount"/>
        <result column="score_total_amount" jdbcType="INTEGER" property="scoreTotalAmount"/>
        <result column="score_total_num" jdbcType="INTEGER" property="scoreTotalNum"/>
        <result column="score_500_count" jdbcType="INTEGER" property="score500Count"/>
        <result column="score_500_amount" jdbcType="INTEGER" property="score500Amount"/>
        <result column="score_2000_count" jdbcType="INTEGER" property="score2000Count"/>
        <result column="score_2000_amount" jdbcType="INTEGER" property="score2000Amount"/>
        <result column="score_5000_count" jdbcType="INTEGER" property="score5000Count"/>
        <result column="score_5000_amount" jdbcType="INTEGER" property="score5000Amount"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, is_subscriber, is_refund, total_refund_amount, refund_num,
        total_pay_amount, pay_num, first_pay_time, last_pay_time, first_vip_time, last_vip_time,
        total_vip_amount, score_recharge_total, score_consume_total, score_consume_total_person,
        score_lose_total, score_balance, score_total_count, score_total_amount, score_total_num,
        score_500_count, score_500_amount, score_2000_count, score_2000_amount, score_5000_count,
        score_5000_amount, update_time
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_payment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            tenant_code,
            user_id,
            is_subscriber,
            is_refund,
            <if test="totalPayAmount != null">
                total_pay_amount,
            </if>
            <if test="payNum != null">
                pay_num,
            </if>
            <if test="firstPayTime != null">
                first_pay_time,
            </if>
            <if test="lastPayTime != null">
                last_pay_time,
            </if>
            <if test="firstVipTime != null">
                first_vip_time,
            </if>
            <if test="lastVipTime != null">
                last_vip_time,
            </if>
            <if test="totalVipAmount != null">
                total_vip_amount,
            </if>
            <if test="scoreRechargeTotal != null">
                score_recharge_total,
            </if>
            <if test="scoreConsumeTotal != null">
                score_consume_total,
            </if>
            <if test="scoreConsumeTotalPerson != null">
                score_consume_total_person,
            </if>
            <if test="scoreLoseTotal != null">
                score_lose_total,
            </if>
            <if test="scoreBalance != null">
                score_balance,
            </if>
            <if test="scoreTotalCount != null">
                score_total_count,
            </if>
            <if test="scoreTotalAmount != null">
                score_total_amount,
            </if>
            <if test="scoreTotalNum != null">
                score_total_num,
            </if>
            <if test="score500Count != null">
                score_500_count,
            </if>
            <if test="score500Amount != null">
                score_500_amount,
            </if>
            <if test="score2000Count != null">
                score_2000_count,
            </if>
            <if test="score2000Amount != null">
                score_2000_amount,
            </if>
            <if test="score5000Count != null">
                score_5000_count,
            </if>
            <if test="score5000Amount != null">
                score_5000_amount,
            </if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{tenantCode,jdbcType=VARCHAR},
            #{userId,jdbcType=VARCHAR},
            <choose>
                <when test="isSubscriber != null">
                    #{isSubscriber,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '0',
                </otherwise>
            </choose>
            <choose>
                <when test="isRefund != null">
                    #{isRefund,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '0',
                </otherwise>
            </choose>
            <if test="totalPayAmount != null">
                #{totalPayAmount,jdbcType=INTEGER},
            </if>
            <if test="payNum != null">
                #{payNum,jdbcType=SMALLINT},
            </if>
            <if test="firstPayTime != null">
                #{firstPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastPayTime != null">
                #{lastPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstVipTime != null">
                #{firstVipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastVipTime != null">
                #{lastVipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="totalVipAmount != null">
                #{totalVipAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreRechargeTotal != null">
                #{scoreRechargeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotal != null">
                #{scoreConsumeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotalPerson != null">
                #{scoreConsumeTotalPerson,jdbcType=INTEGER},
            </if>
            <if test="scoreLoseTotal != null">
                #{scoreLoseTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreBalance != null">
                #{scoreBalance,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalCount != null">
                #{scoreTotalCount,jdbcType=SMALLINT},
            </if>
            <if test="scoreTotalAmount != null">
                #{scoreTotalAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalNum != null">
                #{scoreTotalNum,jdbcType=INTEGER},
            </if>
            <if test="score500Count != null">
                #{score500Count,jdbcType=SMALLINT},
            </if>
            <if test="score500Amount != null">
                #{score500Amount,jdbcType=INTEGER},
            </if>
            <if test="score2000Count != null">
                #{score2000Count,jdbcType=SMALLINT},
            </if>
            <if test="score2000Amount != null">
                #{score2000Amount,jdbcType=INTEGER},
            </if>
            <if test="score5000Count != null">
                #{score5000Count,jdbcType=SMALLINT},
            </if>
            <if test="score5000Amount != null">
                #{score5000Amount,jdbcType=INTEGER},
            </if>
            current_timestamp
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo">
        <!--@mbg.generated-->
        update rp_user_payment_info
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="isSubscriber != null and isSubscriber != ''">
                is_subscriber = #{isSubscriber,jdbcType=CHAR},
            </if>
            <if test="isRefund != null and isRefund != ''">
                is_refund = #{isRefund,jdbcType=CHAR},
            </if>
            <if test="totalRefundAmount != null">
                total_refund_amount = #{totalRefundAmount,jdbcType=INTEGER},
            </if>
            <if test="refundNum != null">
                refund_num = #{refundNum,jdbcType=SMALLINT},
            </if>
            <if test="totalPayAmount != null">
                total_pay_amount = #{totalPayAmount,jdbcType=INTEGER},
            </if>
            <if test="payNum != null">
                pay_num = #{payNum,jdbcType=SMALLINT},
            </if>
            <if test="firstPayTime != null">
                first_pay_time = #{firstPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastPayTime != null">
                last_pay_time = #{lastPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstVipTime != null">
                first_vip_time = #{firstVipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastVipTime != null">
                last_vip_time = #{lastVipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="totalVipAmount != null">
                total_vip_amount = #{totalVipAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreRechargeTotal != null">
                score_recharge_total = #{scoreRechargeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotal != null">
                score_consume_total = #{scoreConsumeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotalPerson != null">
                score_consume_total_person = #{scoreConsumeTotalPerson,jdbcType=INTEGER},
            </if>
            <if test="scoreLoseTotal != null">
                score_lose_total = #{scoreLoseTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreBalance != null">
                score_balance = #{scoreBalance,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalCount != null">
                score_total_count = #{scoreTotalCount,jdbcType=SMALLINT},
            </if>
            <if test="scoreTotalAmount != null">
                score_total_amount = #{scoreTotalAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalNum != null">
                score_total_num = #{scoreTotalNum,jdbcType=INTEGER},
            </if>
            <if test="score500Count != null">
                score_500_count = #{score500Count,jdbcType=SMALLINT},
            </if>
            <if test="score500Amount != null">
                score_500_amount = #{score500Amount,jdbcType=INTEGER},
            </if>
            <if test="score2000Count != null">
                score_2000_count = #{score2000Count,jdbcType=SMALLINT},
            </if>
            <if test="score2000Amount != null">
                score_2000_amount = #{score2000Amount,jdbcType=INTEGER},
            </if>
            <if test="score5000Count != null">
                score_5000_count = #{score5000Count,jdbcType=SMALLINT},
            </if>
            <if test="score5000Amount != null">
                score_5000_amount = #{score5000Amount,jdbcType=INTEGER},
            </if>
            update_time = current_timestamp
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateForStat" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo">
        <!--@mbg.generated-->
        update rp_user_payment_info
        <set>
            <if test="isSubscriber != null and isSubscriber != ''">
                is_subscriber = #{isSubscriber,jdbcType=VARCHAR},
            </if>
            <if test="isRefund != null and isRefund != ''">
                is_refund = #{isRefund,jdbcType=VARCHAR},
            </if>
            <if test="totalRefundAmount != null">
                <if test="totalRefundAmount &gt; 0">
                    refund_num = refund_num + 1,
                </if>
                <if test="totalRefundAmount &lt; 0">
                    refund_num = refund_num - 1,
                </if>
                total_refund_amount = total_refund_amount + #{totalRefundAmount,jdbcType=INTEGER},
            </if>
            <if test="totalPayAmount != null">
                <if test="totalPayAmount &gt; 0">
                    pay_num = pay_num + 1,
                </if>
                <if test="totalPayAmount &lt; 0">
                    pay_num = pay_num - 1,
                </if>
                total_pay_amount = total_pay_amount + #{totalPayAmount,jdbcType=INTEGER},
            </if>
            <if test="lastPayTime != null">
                last_pay_time = #{lastPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="scoreRechargeTotal != null">
                score_recharge_total = score_recharge_total + #{scoreRechargeTotal,jdbcType=INTEGER},
                score_balance = score_balance + #{scoreRechargeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotal != null">
                score_consume_total = score_consume_total + #{scoreConsumeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotalPerson != null">
                score_consume_total_person = score_consume_total_person + #{scoreConsumeTotalPerson,jdbcType=INTEGER},
                score_balance = score_balance - #{scoreConsumeTotalPerson,jdbcType=INTEGER},
            </if>
            <if test="scoreLoseTotal != null">
                score_lose_total = score_lose_total + #{scoreLoseTotal,jdbcType=INTEGER},
                score_balance = score_balance - #{scoreLoseTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalCount != null">
                score_total_count = score_total_count + #{scoreTotalCount,jdbcType=INTEGER},
            </if>
            <if test="scoreTotalAmount != null">
                <if test="scoreTotalAmount &gt; 0">
                    score_total_num = score_total_num + 1,
                </if>
                <if test="scoreTotalAmount &lt; 0">
                    score_total_num = score_total_num - 1,
                </if>
                score_total_amount = score_total_amount + #{scoreTotalAmount,jdbcType=INTEGER},
            </if>
            <if test="score500Amount != null">
                <if test="score500Amount > 0">
                    score_500_count = score_500_count + 1,
                </if>
                <if test="score500Amount &lt; 0">
                    score_500_count = score_500_count - 1,
                </if>
                score_500_amount = score_500_amount + #{score500Amount,jdbcType=INTEGER},
            </if>
            <if test="score2000Amount != null">
                <if test="score2000Amount > 0">
                    score_2000_count = score_2000_count + 1,
                </if>
                <if test="score2000Amount &lt; 0">
                    score_2000_count = score_2000_count - 1,
                </if>
                score_2000_amount = score_2000_amount + #{score2000Amount,jdbcType=INTEGER},
            </if>
            <if test="score5000Amount != null">
                <if test="score5000Amount > 0">
                    score_5000_count = score_5000_count + 1,
                </if>
                <if test="score5000Amount &lt; 0">
                    score_5000_count = score_5000_count - 1,
                </if>
                score_5000_amount = score_5000_amount + #{score5000Amount,jdbcType=INTEGER},
            </if>
            update_time = current_timestamp
        </set>
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rp_user_payment_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="selectScoreBalance" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               sum(score) as score_balance
        from nbchat_user_balance
        group by tenant_code, user_id
    </select>
    <select id="selectScoreDetail" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               abs(sum(if(type = '1', score, null)))                                                               as score_recharge_total,
               abs(sum(if(biz_code = 'dou_expired' and type in ('0', '2', '3'), score, null)))                     as score_lose_total,
               abs(sum(if(biz_code != 'dou_expired' and pay_type = '0' and type in ('0', '2', '3'), score, null))) as score_consume_total_person,
               abs(sum(if(biz_code != 'dou_expired' and pay_type = '1' and type in ('0', '2', '3'), score, null))) as score_consume_total
        from nbchat_user_bill_record
        group by tenant_code, user_id
    </select>
    <select id="selectRefund" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               count(0)       as refund_num,
               sum(pay_price) as total_refund_amount
        from pay_trade_record
        where order_no is null
          and pay_status in ('REFUND_SUCCESS', 'SUCCESS')
        group by tenant_code, user_id
    </select>
    <select id="selectPayInfo" resultMap="BaseResultMap">
        select t1.tenant_code,
               t1.user_id,
               count(0)                                                        as pay_num,
               sum(t1.pay_price)                                               as total_pay_amount,
               min(ifnull(t1.pay_time, t2.order_time))                         as first_pay_time,
               max(ifnull(t1.pay_time, t2.order_time))                         as last_pay_time,
               sum(if(t4.vip_type = '0', 1, 0))                                as score_total_count,
               sum(if(t4.vip_type = '0', t1.pay_price, 0))                     as score_total_amount,
               sum(if(t4.vip_type = '0', t4.score, 0))                         as score_total_num,
               sum(if(t4.vip_type = '0' and t4.score = 500, 1, 0))             as score_500_count,
               sum(if(t4.vip_type = '0' and t4.score = 500, t1.pay_price, 0))  as score_500_amount,
               sum(if(t4.vip_type = '0' and t4.score = 2000, 1, 0))            as score_2000_count,
               sum(if(t4.vip_type = '0' and t4.score = 2000, t1.pay_price, 0)) as score_2000_amount,
               sum(if(t4.vip_type = '0' and t4.score = 5000, 1, 0))            as score_5000_count,
               sum(if(t4.vip_type = '0' and t4.score = 5000, t1.pay_price, 0)) as score_5000_amount
        from pay_trade_record t1
                 left join pay_order t2 on t2.order_no = t1.order_no
                 left join pay_order_item t3 on t3.order_no = t1.order_no
                 left join pay_goods_sku_dd_conf t4 on t4.sku_id = t3.sku_id
        where t1.refund_no = ''
          and t1.pay_status in ('SUCCESS', 'TRADE_SUCCESS')
          and t1.pay_type != 'DOU'
        group by t1.tenant_code, t1.user_id
    </select>
    <select id="selectScoreBalanceByUserId" resultMap="BaseResultMap">
        select tenant_code,
        user_id,
        sum(score) as score_balance
        from nbchat_user_balance
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="selectScoreDetailByUserId" resultMap="BaseResultMap">
        select tenant_code,
        user_id,
        abs(sum(if(type = '1', score, null))) as score_recharge_total,
        abs(sum(if(biz_code = 'dou_expired' and type in ('0', '2', '3'), score, null))) as score_lose_total,
        abs(sum(if(biz_code != 'dou_expired' and pay_type = '0' and type in ('0', '2', '3'), score, null))) as score_consume_total_person,
        abs(sum(if(biz_code != 'dou_expired' and pay_type = '1' and type in ('0', '2', '3'), score, null))) as score_consume_total
        from nbchat_user_bill_record
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="selectRefundByUserId" resultMap="BaseResultMap">
        select tenant_code,
        user_id,
        count(0) as refund_num,
        sum(pay_price) as total_refund_amount
        from pay_trade_record
        where order_no is null
        and pay_status in ('REFUND_SUCCESS', 'SUCCESS')
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="selectPayInfoByUserId" resultMap="BaseResultMap">
        select t1.tenant_code,
        t1.user_id,
        count(0) as pay_num,
        sum(t1.pay_price) as total_pay_amount,
        min(ifnull(t1.pay_time, t2.order_time)) as first_pay_time,
        max(ifnull(t1.pay_time, t2.order_time)) as last_pay_time,
        sum(if(t4.vip_type = '0', 1, 0)) as score_total_count,
        sum(if(t4.vip_type = '0', t1.pay_price, 0)) as score_total_amount,
        sum(if(t4.vip_type = '0', t4.score, 0)) as score_total_num,
        sum(if(t4.vip_type = '0' and t4.score = 500, 1, 0)) as score_500_count,
        sum(if(t4.vip_type = '0' and t4.score = 500, t1.pay_price, 0)) as score_500_amount,
        sum(if(t4.vip_type = '0' and t4.score = 2000, 1, 0)) as score_2000_count,
        sum(if(t4.vip_type = '0' and t4.score = 2000, t1.pay_price, 0)) as score_2000_amount,
        sum(if(t4.vip_type = '0' and t4.score = 5000, 1, 0)) as score_5000_count,
        sum(if(t4.vip_type = '0' and t4.score = 5000, t1.pay_price, 0)) as score_5000_amount
        from pay_trade_record t1
        left join pay_order t2 on t2.order_no = t1.order_no
        left join pay_order_item t3 on t3.order_no = t1.order_no
        left join pay_goods_sku_dd_conf t4 on t4.sku_id = t3.sku_id
        where t1.refund_no = ''
        and t1.pay_status in ('SUCCESS', 'TRADE_SUCCESS')
        and t1.tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and t1.user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteAll">
        delete
        from rp_user_payment_info
    </delete>
</mapper>
