<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserExamInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserExamInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_exam_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="exam_make_times" jdbcType="INTEGER" property="examMakeTimes"/>
        <result column="exam_chick_times" jdbcType="INTEGER" property="examChickTimes"/>
        <result column="exam_download_times" jdbcType="INTEGER" property="examDownloadTimes"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, exam_make_times, exam_chick_times, exam_download_times, update_time
    </sql>
    <insert id="batchInsertForTimer" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_exam_info
        (tenant_code, user_id, exam_make_times, exam_chick_times, exam_download_times, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.examMakeTimes,jdbcType=INTEGER},
            #{item.examChickTimes,jdbcType=INTEGER}, #{item.examDownloadTimes,jdbcType=INTEGER},
            current_timestamp)
        </foreach>
    </insert>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_user_exam_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
    <update id="batchUpdateForTimer">
        <!--@mbg.generated-->
        update rp_user_exam_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="exam_make_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.examMakeTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            exam_make_times +
                        </if>
                        #{item.examMakeTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="exam_chick_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.examChickTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            exam_chick_times +
                        </if>
                        #{item.examChickTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="exam_download_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.examDownloadTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            exam_download_times +
                        </if>
                        #{item.examDownloadTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then current_timestamp
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="insertForAll">
        insert into rp_user_exam_info (tenant_code, user_id, exam_make_times, exam_chick_times, exam_download_times,
                                       update_time)
        select orud.tenant_code,
               orud.user_id,
               ifnull(ecr.make_times, 0),
               ifnull(suol.chick_times, 0),
               ifnull(suol.download_times, 0),
               current_timestamp
        from op_rp_user_detail orud
                 left join
             (select tenant_code,
                     user_id,
                     sum(fill_num + judge_num + choice_m_num + choice_s_num + question_num) as make_times
              from exam_creation_record
              group by tenant_code, user_id) ecr
             on ecr.tenant_code = orud.tenant_code
                 and ecr.user_id = orud.user_id
                 left join
             (select tenant_code,
                     user_id,
                     sum(if(type = 'exam_gene', 1, 0))   as chick_times,
                     sum(if(type = 'exam_export', 1, 0)) as download_times
              from sys_user_operate_log
              where type in ('exam_gene', 'exam_export')
              group by tenant_code, user_id, type) suol
             on suol.tenant_code collate utf8mb4_general_ci = orud.tenant_code
                 and suol.user_id collate utf8mb4_general_ci = orud.user_id
        where (ecr.make_times is not null and ecr.make_times != 0)
           or (suol.chick_times is not null and suol.chick_times != 0)
           or (suol.download_times is not null and suol.download_times != 0)
    </insert>
    <delete id="deleteAll">
        delete
        from rp_user_exam_info
    </delete>
</mapper>
