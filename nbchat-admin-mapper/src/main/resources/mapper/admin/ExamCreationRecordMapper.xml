<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.ExamCreationRecordMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.ExamCreationRecord">
        <!--@mbg.generated-->
        <!--@Table exam_creation_record-->
        <id column="creation_id" jdbcType="VARCHAR" property="creationId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="creation_name" jdbcType="VARCHAR" property="creationName"/>
        <result column="fill_num" jdbcType="INTEGER" property="fillNum"/>
        <result column="judge_num" jdbcType="INTEGER" property="judgeNum"/>
        <result column="choice_m_num" jdbcType="INTEGER" property="choiceMNum"/>
        <result column="choice_s_num" jdbcType="INTEGER" property="choiceSNum"/>
        <result column="question_num" jdbcType="INTEGER" property="questionNum"/>
        <result column="question_content" jdbcType="VARCHAR" property="questionContent"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="file_content" jdbcType="LONGVARCHAR" property="fileContent"/>
        <result column="creation_content" jdbcType="LONGVARCHAR" property="creationContent"/>
        <result column="creation_type" jdbcType="CHAR" property="creationType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="is_valid" jdbcType="CHAR" property="isValid"/>
        <result column="examMakeTimes" jdbcType="INTEGER" property="examMakeTimes"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        creation_id, tenant_code, user_id, creation_name, fill_num, judge_num, choice_m_num,
        choice_s_num, question_num, question_content, file_url, file_content, creation_content,
        creation_type, create_time, create_by, update_time, update_by, is_valid
    </sql>

    <select id="countByGroupUserIdWhenCreateTimeBetween" resultMap="BaseResultMap">
        select tenant_code,
        user_id,
        sum(fill_num + judge_num + choice_m_num + choice_s_num + question_num) as examMakeTimes
        from exam_creation_record
        where create_time &lt; #{startTime,jdbcType=TIMESTAMP}
        <if test="endTime != null">
            and create_time >= #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by tenant_code, user_id
    </select>
</mapper>
