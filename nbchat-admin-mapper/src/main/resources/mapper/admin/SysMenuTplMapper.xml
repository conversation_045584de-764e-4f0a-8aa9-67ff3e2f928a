<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysMenuTplMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysMenuTpl">
        <id column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="tpl_name" property="tplName" jdbcType="VARCHAR"/>
        <result column="tpl_type" property="tplType" jdbcType="CHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        tpl_code, tpl_name, remark, is_valid, create_time, update_time, tpl_type
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_tpl
        where tpl_code = #{tplCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sys_menu_tpl
        where tpl_code = #{tplCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTpl">
        insert into sys_menu_tpl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="tplName != null">
                tpl_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="tplType != null">
                tpl_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="tplName != null">
                #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tplType != null">
                #{tplType,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTpl">
        update sys_menu_tpl
        <set>
            <if test="tplName != null and tplName != ''">
                tpl_name = #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where tpl_code = #{tplCode,jdbcType=VARCHAR}
    </update>

    <select id="selectByCondition" resultMap="BaseResultMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTpl">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_tpl
        <where>
            <if test="tplName != null and tplName !=''">
                and tpl_name like concat('%', #{tplName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="tplCode != null and tplCode != ''">
                and tpl_code = #{tplCode,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
            <if test="tplType != null and tplType != ''">
                and tpl_type = #{tplType,jdbcType=CHAR}
            </if>
        </where>
        ORDER BY tpl_name desc
    </select>
</mapper>