<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysTplUserRelMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    <!--@mbg.generated-->
    <!--@Table sys_tenant_subsystem-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tpl_code" jdbcType="VARCHAR" property="tplCode" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tpl_code, user_id, create_time, create_by, update_time, update_by
  </sql>
  <select id="list" parameterType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO" resultType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    select
    userRel.id,
    userRel.tpl_code tplCode,
    userRel.user_id userId,
    COALESCE(nullif(nbUser.real_name,''),nbUser.name) userRealityName,
    nbUser.phone phone,
    menuTpl.tpl_name tplName
    from sys_tpl_user_rel userRel 
    left join NBCHAT_USER nbUser on userRel.user_id = nbUser.user_id
    left join sys_menu_tpl menuTpl on userRel.tpl_code = menuTpl.tpl_code
    <where>
      <if test="tplName != null and tplName != ''">
        and menuTpl.tpl_name like CONCAT('%', #{tplName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="userRealityName != null and userRealityName != ''">
        and nbUser.real_name like CONCAT('%', #{userRealityName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="phone != null and phone != ''">
        and nbUser.phone like CONCAT('%', #{phone,jdbcType=VARCHAR}, '%')
      </if>
    </where>
    order by userRel.create_time
    <if test="sort==1">ASC</if>
    <if test="sort!=1">DESC</if>
  </select>

  <select id="selectInfoById" parameterType="java.lang.Integer" resultType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    select
    userRel.id,
    userRel.tpl_code tplCode,
    userRel.user_id userId,
    COALESCE(nullif(nbUser.real_name,''),nbUser.name) userRealityName,
    nbUser.phone phone,
    menuTpl.tpl_name tplName
    from sys_tpl_user_rel userRel 
    left join NBCHAT_USER nbUser on userRel.user_id = nbUser.user_id
    left join sys_menu_tpl menuTpl on userRel.tpl_code = menuTpl.tpl_code
    where userRel.id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    insert into sys_tpl_user_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="tplCode != null">tpl_code,</if>
      <if test="userId != null">user_id,</if>
      <if test="createTime != null">create_time,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateTime != null">update_time,</if>
      <if test="updateBy != null">update_by,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=INTEGER},</if>
      <if test="tplCode != null">#{tplCode,jdbcType=VARCHAR},</if>
      <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
      <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
      <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
      <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
      <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
    </trim>
  </insert>

  <select id="selectInfoByUserIdCount" parameterType="java.lang.String" resultType="java.lang.Integer">
    select
      count(0)
    from sys_tpl_user_rel
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>

  <select id="selectInfoByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_tpl_user_rel
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>

  <update id="updateByIdSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    update sys_tpl_user_rel
    <set>
      <if test="tplCode != null">tpl_code = #{tplCode,jdbcType=VARCHAR},</if>
      <if test="updateTime != null">update_time= #{updateTime,jdbcType=TIMESTAMP},</if>
      <if test="updateBy != null">update_by = #{updateBy,jdbcType=VARCHAR},</if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteById">
    <!--@mbg.generated-->
    delete from sys_tpl_user_rel where id = #{id,jdbcType=INTEGER}
  </delete>

  <update id="updateRealName" parameterType="com.tydic.nbchat.admin.mapper.po.SysTplUserRelBO">
    UPDATE nbchat_user set real_name = #{userRealityName,jdbcType=VARCHAR},updated_time=NOW() WHERE user_id = #{userId,jdbcType=VARCHAR}
  </update>
</mapper>