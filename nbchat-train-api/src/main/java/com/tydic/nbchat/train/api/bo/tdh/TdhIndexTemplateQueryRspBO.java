package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhIndexTemplateQueryRspBO implements Serializable {
    /**
     * 主键
     */
    private String tplId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 模板名称
     */
    private String tplName;

    /**
     * 模板描述
     */
    private String tplDesc;

    /**
     * 配置
     */
    private String tplConfig;

    /**
     * 配置
     */
    private String tplContent;

    /**
     * 类型：ppt/exam ppt模板/试题模板
     */
    private String tplType;

    private Integer orderIndex;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否有效 0无效 1有效
     */
    private String isValid;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 尺寸
     */
    private String tplSize;

    /**
     * 上架状态：0-未上架，1-已上架
     */
    private String status;

    /**
     * 页签类型：homeZone、首页灵感专区 landingPage、ppt转视频落地页
     */
    private String tag;
}
