spring:
  redis:
    host: ************** #************** #127.0.0.1
    port: 6389 #16379
    password: Redis235 #Redis235 #Mtj1qJSwLF
    timeout: 1000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-wait: -1
        max-active: 1000
        max-idle: 10
        min-idle: 5
    database: 5
  datasource:
    druid:
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      primary: master
      druid:
        initial-size: 3
        max-active: 10
        min-idle: 2
        max-wait: -1
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 30000
        time-between-eviction-runs-millis: 60000
        validation-query: select 1
        validation-query-timeout: -1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        pool-prepared-statements: true
        max-open-prepared-statements: 100
        filters: stat,wall
        share-prepared-statements: true
        wall:
          multi-statement-allow: true
      datasource:
        master:
          username: dml_user
          password: QfNWG4di_dml_dev
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************
#          username: nbchat
#          password: nbchat#2024!23
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *************************************************************************************************************************************************************************************************************************

# mybatis
mybatis:
  mapper-locations: classpath:mapper/*/*.xml

# 分页组件
pagehelper:
  helperDialect: mysql
  params: count=countSql

nacos:
  config:
    server-addr: **************:8848

# dubbo 服务配置
dubbo:
  application:
    name: nbchat-user
  registry:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    # 可以通过url ?namespace=nicc-env-dev&group=NICC的形式配置，也可以通过这种parameters配置
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  provider:
    threads: 300
    threadpool: cached
    loadbalance: roundrobin
    version: 1.0
    group: NICC
  protocol:
    name: dubbo
    port: -1
  # 配置中心和元数据
  config-center:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  metadata-report:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  consumer:
    check: false
    version: 1.0
    group: NICC

nicc-dc-config:
  dubbo-provider:
    version: 1.0
    group: NICC

# 数据中心相关组件配置
nicc-plugin:
  redis:
    enable: true # 是否开启redisHelper，开启后必须配置spring.redis，同时直接用RedisHelper
  oss:
    enable: true
    endpoint: http://xxxx.ops.super.guangzhou.unicom.local:8100/
    access-key: ak
    access-key-secret: sk
    bucket-name: nbchat-dev
  rest:
    enable: true # 开启restTemplate 组件
    connect-timeout: 5000
    read-timeout: 5000
    print-log: true
  ftp:
    enable: false
  kkmq:
    mq-type: rocketmq
    log-level: warn
    name-server: **************:9878 # rocketmq服务地址
    producer:
      group: nbchat_user # 自定义的组名称
logging:
  level:
    com.tydic.nbchat.user.mapper: debug

nbchat-user:
  config:
    dingtalk-enable: true
    dingtalk-title: "【测试数据】\n"
    dingtalk-server-url: "https://oapi.dingtalk.com/robot/send?access_token=1a42390a2d2754b83126dc186ce94e89f65fb5c595d3608900ab609599526174"
    dingtalk-send-map: {"tdh-portal":"***********", "tdh-admin":"***********, ***********"}
    enable-timer: false # 是否开启定时任务
    auth-token-key: key2
    default-user-settings: { "robotType":"chatgpt"  } # 用户默认设置项
    bind-setting-url: "https://chat-test.tydiczt.com/chat/?setRobot={code}" # 个性化配置绑定链接
    bind-setting-default: { "robotType":"chatgpt"  } # 个性化默认初始化设置
    wchat:
      channels:
        pc:
          app-id:
          secret:
          name: pc扫码登录
        official_accounts:
          name: 微信公众号
          app-id:
          secret:
          token-url:
        tdh_mp:
          name: 微信小程序
          app-id:
          secret:
          token-url:
      wx-qrcode-url: https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=
      wx-user-info-url: https://api.weixin.qq.com/cgi-bin/user/info?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN
      wx-send-msg: https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=ACCESS_TOKEN # 发送客服消息调用地址
      wx-scan-push-image: S91KPaUlPQR1epHb4ipVCl3BPQydP3neHE7nJuJm7-oE5rBukOgrXomwy9fLkgQp # 微信扫码推送图片
      following-content: AI课件制作，就用“课件帮”\n\r终于等到你~！\n\r“课件帮”一站式智能服务平台\n\r3步即可完成视频课件制作\n\r轻松做课件！\uD83C\uDF39"
      template-msg-url: https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN
      templates:
        recharge:
          template-id:
          desc: 充值成功通知
          keys:
            - character_string2   #单号
            - amount5             #充值金额
            - thing16             #充值项目
        video:
          template-id:
          desc: 视频制作完成通知
          keys:
            - thing5      #产品名称
            - const6      #枚举值：已制作完成
            - time7       #完成时间
        custom_cancel:
          template-id:
          desc: 取消定制
          keys:
            - character_string4   #订单号
            - thing5              #产品名称
            - const6              #枚举值：已成功取消定制
            - time7               #完成时间
        custom_complete:
          template-id:
          desc: 定制完成
          keys:
            - character_string4   #订单号
            - thing5              #产品名称
            - const6              #枚举值：已成功取消定制
            - time7               #完成时间
            - time13              #到期时间
    sms:
      accessKeyId: LTAI5tAC4xBZyX4YTkEy8yzh
      accessKeySecret: ******************************
      region: cn-beijing
      signName: 迪问
      endpointOverride: dysmsapi.aliyuncs.com
      # 短信模板配置 - JSON风格
      templates:
        defaultTemplates: {
          "LOGIN": "SMS_148860011",
          "TENANT_APPLY": "SMS_463131159",
          "VIDEO_FINISH": "SMS_464205448",
          "VIDEO_DKZN_FINISH": "SMS_465366522",
          "VIP_OPEN": "SMS_470875038",
          "VIP_CLOSE": "SMS_475250049",
          "VIP_RENEW": "SMS_470695054",
          "RECHARGE": "SMS_470830040",
          "HUMAN_CUSTOMIZATION_COMPLETED": "SMS_473875205",
          "HUMAN_CUSTOMIZATION_CANCEL": "SMS_473850236",
          "VOICE_CUSTOMIZATION_COMPLETED": "SMS_473865235",
          "VOICE_CUSTOMIZATION_CANCEL": "SMS_474035098",
          "HUMAN_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_483180144",
          "VOICE_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_483225137",
          "SMS_VIP_PROFESSIONAL_OPEN_CODE": "SMS_489645300",
          "SMS_VIP_PROFESSIONAL_MAX_OPEN_CODE": "SMS_489795286"
        }
        # 区号特定模板配置
        area_codes: {
          # 中国大陆(86)使用默认模板
          "86": {},
          # 中国香港(852)模板映射
          "852": {
            "LOGIN": "SMS_485820196",
            "TENANT_APPLY": "SMS_485875200",
            "VIDEO_FINISH": "SMS_485785227",
            "VIDEO_DKZN_FINISH": "SMS_485845217",
            "VIP_OPEN": "SMS_485685228",
            "VIP_CLOSE": "SMS_485755209",
            "VIP_RENEW": "SMS_485860182",
            "RECHARGE": "SMS_485690221",
            "HUMAN_CUSTOMIZATION_COMPLETED": "SMS_485675195",
            "HUMAN_CUSTOMIZATION_CANCEL": "SMS_485755208",
            "VOICE_CUSTOMIZATION_COMPLETED": "SMS_485735184",
            "VOICE_CUSTOMIZATION_CANCEL": "SMS_485845221",
            "HUMAN_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_485825208",
            "VOICE_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_485875215"
          }
        }
    captcha:
      type: gif
      width: 120
      height: 45
      code-count: 4
      line-count: 5
    yuewen-login-url: https://yuewen.unicomgd.com:3100/wecom-test/gePhoneByUniqueNo?uniqueNo={data}
    suyan:
      enable: true
      accessKeyId: P6dYBjq9WLsQ0ATJy3ohyT0f0TSt
      accessKeySecret: 8TGgznaCvxejFKxRANAJ1nIBC3dnmp
      appId: 9c8fe7ad2cdb4174a2a39f2450dc3f4a
      appSecret: 73cb9c7102d04c9e81dfb9c6ad505afe
      baseUrl: https://ecloud.10086.cn
      validateAuthCode: /api/cem/cem_sso/clientAuth/sideAppCall/v1/validateAuthCode
      renewAuthToken: /api/cem/cem_sso/clientAuth/sideAppCall/v1/renewAuthToken
      queryEcloudComputerUser: /api/cem/cem_sso/clientAuth/sideAppCall/v1/queryEcloudComputerUser

jasypt:
  encryptor:
    password: nicc-salt_!@#
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator